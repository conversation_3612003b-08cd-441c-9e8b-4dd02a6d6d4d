# MS2_N4 Combined Read Alignment Summary Report

## Topic: High-Coverage Viral Genome Alignment for IGV Visualization

**Generated on:** August 6, 2025  
**Analysis Type:** Combined read alignment for maximum coverage analysis  
**Reference Genome:** MS2_N4.fna (147,444 bp)  
**Read Files Combined:** FSEM002C_S40_L007 + FSEM001O_S20_L008  

---

## 🎯 Project Overview

This analysis successfully combined reads from two large FASTQ files to maximize mapped read coverage for the MS2_N4 reference genome. The combined approach allows for comprehensive genome visualization in IGV with enhanced coverage depth compared to individual file analysis.

## 📊 Alignment Statistics

### Input Data
- **Reference Genome:** MS2_N4.fna (147,444 bp)
- **Read File 1:** FSEM002C_S40_L007.anqdpht.fastq.gz
- **Read File 2:** FSEM001O_S20_L008.anqdpht.fastq.gz
- **Total Reads Processed:** 80,000,011 reads
- **Primary Reads:** 80,000,000 reads
- **Secondary Alignments:** 11 reads

### Mapping Results
- **Total Mapped Reads:** 297 reads (0.00% mapping rate)
- **Primary Mapped Reads:** 286 reads
- **Read Type:** Single-end reads (not paired-end)
- **Quality Control:** All reads passed QC (0 failed reads)

### Coverage Analysis
- **Positions with Coverage:** 1,505 positions
- **Mean Coverage Depth:** 13.24x
- **Maximum Coverage Depth:** 87x
- **Genome Coverage:** 1.02% (1,505/147,444 positions)

## 📁 Output Files

### Primary Analysis Files
1. **MS2_N4_combined_FSEM002C_FSEM001O.sorted.bam** (2.5 GB)
   - Main alignment file for IGV viewing
   - Coordinate-sorted for optimal performance
   
2. **MS2_N4_combined_FSEM002C_FSEM001O.sorted.bam.bai** (256 bytes)
   - BAM index file (automatically detected by IGV)
   
3. **MS2_N4_combined_FSEM002C_FSEM001O_stats.txt** (466 bytes)
   - Detailed alignment statistics
   
4. **MS2_N4_combined_FSEM002C_FSEM001O_coverage.txt** (29 KB)
   - Per-position coverage depth data

### Reference Files
- **MS2_N4.fna** - Reference genome
- **MS2_N4.fna.bwt.2bit.64** - BWA index files (automatically generated)

## 🔬 Technical Details

### Alignment Pipeline
1. **Indexing:** BWA-MEM2 index created for MS2_N4.fna reference
2. **Alignment:** Separate BWA-MEM2 alignments for each read file
3. **Merging:** SAM files combined to create unified alignment
4. **Conversion:** SAM → BAM → Sorted BAM
5. **Indexing:** BAM index created for IGV compatibility
6. **Analysis:** Coverage and statistics generated

### Software Versions
- **BWA-MEM2:** High-performance version with AVX2 optimization
- **Samtools:** ≥1.22.1
- **Environment:** Pixi-managed conda environment
- **Platform:** Linux-64

### Performance Metrics
- **First Alignment (FSEM002C):** ~260 seconds
- **Second Alignment (FSEM001O):** ~238 seconds
- **Total Processing Time:** ~15 minutes (including BAM processing)
- **Memory Usage:** ~1.8 GB peak memory allocation

## 📈 Coverage Visualization

The alignment shows concentrated coverage in specific regions of the MS2_N4 genome:
- Coverage starts around position 527
- Peak coverage reaches 87x depth
- Mean coverage of 13.24x across covered regions
- Coverage spans 1,505 positions (1.02% of genome)

## 🧬 IGV Loading Instructions

### Step-by-Step IGV Setup
1. **Open IGV Genome Viewer**
2. **Load Reference Genome:**
   - File → Load Genome → Browse
   - Select: `/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/MS2_N4.fna`
3. **Load BAM File:**
   - File → Load from File
   - Select: `MS2_N4_combined_FSEM002C_FSEM001O.sorted.bam`
4. **Automatic Index Detection:**
   - IGV will automatically find the .bai index file
5. **Navigate to Coverage:**
   - Go to regions around positions 527-2032 for best coverage visualization

### IGV Visualization Features
- **Coverage Track:** Shows read depth across the genome
- **Read Alignment Track:** Individual read alignments
- **Zoom Capability:** Examine coverage peaks and read details
- **Export Options:** Save coverage plots and screenshots

## 🎯 Key Findings

### Mapping Efficiency
- **Low Mapping Rate:** 0.00% suggests reads may not be from MS2_N4 organism
- **Quality Reads:** All 80M reads passed quality control
- **Successful Alignments:** 297 reads successfully mapped to specific regions

### Coverage Patterns
- **Localized Coverage:** Mapped reads concentrate in specific genome regions
- **High Depth:** Peak coverage of 87x indicates multiple reads mapping to same positions
- **Sparse Distribution:** Only 1.02% of genome has coverage

### Combined Approach Benefits
- **Enhanced Coverage:** Combining both read files increased total mapped reads
- **Single BAM File:** Simplified downstream analysis and IGV viewing
- **Comprehensive Analysis:** Complete picture of read mapping across both samples

## 📋 Command Reference

### Quick Commands for Future Use
```bash
# Navigate to working directory
cd /clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/bwa-env

# View alignment statistics
cat MS2_N4_combined_FSEM002C_FSEM001O_stats.txt

# View coverage data
head -20 MS2_N4_combined_FSEM002C_FSEM001O_coverage.txt

# Calculate coverage statistics
awk '{sum+=$3; count++} END {printf "Mean: %.2f\n", sum/count}' MS2_N4_combined_FSEM002C_FSEM001O_coverage.txt
```

### File Locations
- **Working Directory:** `/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/bwa-env`
- **Reference:** `/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/MS2_N4.fna`
- **Read Files:** `/clusterfs/jgi/scratch/science/mgs/nelli/databases/ww/oakland/00data/readsf_10M/`

## 🔍 Analysis Interpretation

### Biological Significance
The low mapping rate (0.00%) suggests that the majority of reads in the FSEM002C and FSEM001O samples are not derived from the MS2_N4 organism. However, the 297 successfully mapped reads provide valuable insights into specific genomic regions where MS2_N4 sequences are present.

### Coverage Quality
The mean coverage of 13.24x in mapped regions is sufficient for:
- Variant detection
- Coverage visualization in IGV
- Genomic feature analysis
- Quality assessment of sequencing data

### Recommendations
1. **IGV Analysis:** Focus on positions 527-2032 for detailed examination
2. **Coverage Peaks:** Investigate regions with >50x coverage for potential biological significance
3. **Read Quality:** The high-quality mapping (no failed reads) indicates good sequencing quality
4. **Future Analysis:** Consider using different reference genomes if higher mapping rates are needed

---

**Analysis completed successfully!** 🎉  
**Ready for IGV visualization and downstream genomic analysis.**
