#!/bin/bash

# CRISPR Guide RNA Design Pipeline - Final Version
# ===============================================
# 
# Advanced CRISPR guide RNA design from conserved regions in viral sequences
# Designed for senior bioinformaticians working with viral genome analysis
#
# Author: Senior Bioinformatics Pipeline
# Date: August 2025

set -euo pipefail

# Default parameters
INPUT_FILE=""
OUTPUT_DIR="crispr_guides_final"
MAX_GUIDES=10
SEPARATION=150
CONSERVATION=0.5
GUIDE_LENGTH=20
GC_MIN=20
GC_MAX=80
VERBOSE=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

print_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

print_info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Function to show usage
show_usage() {
    cat << EOF
CRISPR Guide RNA Design Pipeline - Final Version

USAGE:
    $0 -i <input_fasta> [OPTIONS]

REQUIRED:
    -i, --input FILE        Input FASTA file with viral sequences (e.g., pathdb.fna)

OPTIONS:
    -o, --output DIR        Output directory (default: crispr_guides_final)
    -n, --max-guides NUM    Maximum number of guide RNAs (default: 10)
    -s, --separation NUM    Minimum separation between guides in bp (default: 150)
    -c, --conservation NUM  Minimum conservation score 0-1 (default: 0.5)
    -l, --length NUM        Guide RNA length (default: 20)
    --gc-min NUM           Minimum GC content percentage (default: 20)
    --gc-max NUM           Maximum GC content percentage (default: 80)
    -v, --verbose          Verbose output
    -h, --help             Show this help message

EXAMPLES:
    # Basic usage with pathdb.fna
    $0 -i pathdb.fna

    # Design 50 guide RNAs with high conservation
    $0 -i pathdb.fna -n 50 -c 0.8 -s 200

    # Custom GC content range for diverse sequences
    $0 -i pathdb.fna --gc-min 30 --gc-max 70 -o viral_guides

    # Verbose output for debugging
    $0 -i pathdb.fna -v

OUTPUT FILES:
    guide_rnas.fasta       - FASTA format sequences ready for ordering
    guide_rnas.csv         - Detailed information for each guide RNA
    guide_rna_summary.txt  - Summary statistics and top guides

EOF
}

# Function to validate parameters
validate_parameters() {
    # Check input file
    if [[ ! -f "$INPUT_FILE" ]]; then
        print_error "Input file '$INPUT_FILE' not found"
        exit 1
    fi

    # Check if input file is FASTA format
    if ! head -1 "$INPUT_FILE" | grep -q "^>"; then
        print_error "Input file does not appear to be in FASTA format"
        exit 1
    fi

    # Validate numeric parameters
    if [[ $MAX_GUIDES -lt 1 ]]; then
        print_error "Maximum guides must be at least 1"
        exit 1
    fi

    if [[ $SEPARATION -lt 1 ]]; then
        print_error "Separation must be at least 1 bp"
        exit 1
    fi
}

# Function to check dependencies
check_dependencies() {
    print_info "Checking dependencies..."
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is required but not found"
        exit 1
    fi

    # Check if the Python script exists
    if [[ ! -f "crispr_guide_design_final.py" ]]; then
        print_error "crispr_guide_design_final.py not found in current directory"
        print_info "Make sure both scripts are in the same directory"
        exit 1
    fi

    print_status "Dependencies check passed"
}

# Function to analyze input file
analyze_input() {
    print_info "Analyzing input file: $INPUT_FILE"
    
    local seq_count=$(grep -c "^>" "$INPUT_FILE" || echo "0")
    local file_size=$(du -h "$INPUT_FILE" | cut -f1)
    
    print_info "Sequences found: $seq_count"
    print_info "File size: $file_size"
    
    if [[ $seq_count -eq 0 ]]; then
        print_error "No sequences found in input file"
        exit 1
    fi
    
    if [[ $seq_count -eq 1 ]]; then
        print_warning "Only 1 sequence found - conservation analysis may not be meaningful"
        print_info "Consider using multiple related viral sequences for better conservation analysis"
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -i|--input)
            INPUT_FILE="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -n|--max-guides)
            MAX_GUIDES="$2"
            shift 2
            ;;
        -s|--separation)
            SEPARATION="$2"
            shift 2
            ;;
        -c|--conservation)
            CONSERVATION="$2"
            shift 2
            ;;
        -l|--length)
            GUIDE_LENGTH="$2"
            shift 2
            ;;
        --gc-min)
            GC_MIN="$2"
            shift 2
            ;;
        --gc-max)
            GC_MAX="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if input file is provided
if [[ -z "$INPUT_FILE" ]]; then
    print_error "Input file is required"
    show_usage
    exit 1
fi

# Main execution
main() {
    print_status "=== CRISPR Guide RNA Design Pipeline - Final Version ==="
    print_info "Starting CRISPR guide RNA design pipeline"
    
    # Check dependencies
    check_dependencies
    
    # Validate parameters
    validate_parameters
    
    # Analyze input
    analyze_input
    
    # Show parameters
    print_info "Pipeline parameters:"
    print_info "  Input file: $INPUT_FILE"
    print_info "  Output directory: $OUTPUT_DIR"
    print_info "  Maximum guides: $MAX_GUIDES"
    print_info "  Minimum separation: $SEPARATION bp"
    print_info "  Conservation threshold: $CONSERVATION"
    print_info "  Guide length: $GUIDE_LENGTH"
    print_info "  GC content range: $GC_MIN% - $GC_MAX%"
    
    # Create output directory
    mkdir -p "$OUTPUT_DIR"
    
    # Run Python script
    print_status "Running CRISPR guide design..."
    
    local python_cmd="python3 crispr_guide_design_final.py"
    python_cmd+=" --input '$INPUT_FILE'"
    python_cmd+=" --output '$OUTPUT_DIR'"
    python_cmd+=" --max-guides $MAX_GUIDES"
    python_cmd+=" --separation $SEPARATION"
    python_cmd+=" --conservation $CONSERVATION"
    python_cmd+=" --length $GUIDE_LENGTH"
    python_cmd+=" --gc-min $GC_MIN"
    python_cmd+=" --gc-max $GC_MAX"
    
    if [[ "$VERBOSE" == "true" ]]; then
        print_info "Executing: $python_cmd"
    fi
    
    if eval "$python_cmd"; then
        print_status "CRISPR guide RNA design completed successfully!"
        print_info "Results saved to: $OUTPUT_DIR/"
        
        # Show quick summary
        if [[ -f "$OUTPUT_DIR/guide_rnas.fasta" ]]; then
            local guide_count=$(grep -c "^>" "$OUTPUT_DIR/guide_rnas.fasta" || echo "0")
            print_status "Generated $guide_count guide RNAs"
            
            # Show first few guides
            print_info "Top guide RNAs:"
            head -10 "$OUTPUT_DIR/guide_rnas.fasta" | while read line; do
                if [[ $line == ">"* ]]; then
                    print_info "  $line"
                else
                    print_info "    $line"
                fi
            done
        fi
        
        print_info "Output files:"
        print_info "  - $OUTPUT_DIR/guide_rnas.fasta (sequences for ordering)"
        print_info "  - $OUTPUT_DIR/guide_rnas.csv (detailed information)"
        print_info "  - $OUTPUT_DIR/guide_rna_summary.txt (summary report)"
        
        print_status "Pipeline completed! Ready for CRISPR targeting experiments."
        
    else
        print_error "CRISPR guide design failed"
        exit 1
    fi
}

# Run main function
main "$@"
