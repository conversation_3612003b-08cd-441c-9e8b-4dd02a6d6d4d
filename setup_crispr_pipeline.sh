#!/bin/bash

# CRISPR Guide RNA Design Pipeline - Setup Script
# ==============================================
# 
# Automated setup script for the CRISPR guide RNA design pipeline
# Handles file copying, permission setting, and validation
#
# Author: Senior Bioinformatics Pipeline
# Date: August 2025

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default target directory
TARGET_DIR="/clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign"
SOURCE_DIR="$(pwd)"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

print_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

print_info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Function to show usage
show_usage() {
    cat << EOF
CRISPR Guide RNA Design Pipeline - Setup Script

USAGE:
    $0 [OPTIONS]

OPTIONS:
    -t, --target DIR    Target directory (default: $TARGET_DIR)
    -s, --source DIR    Source directory (default: current directory)
    -h, --help          Show this help message

DESCRIPTION:
    This script sets up the CRISPR guide RNA design pipeline by:
    1. Creating the target directory if it doesn't exist
    2. Copying all required files
    3. Setting proper permissions
    4. Validating the installation
    5. Testing with pathdb.fna if available

FILES COPIED:
    - crispr_guide_design_final.py      (Main Python script)
    - run_crispr_pipeline_final.sh      (Bash wrapper)
    - CRISPR_Pipeline_Documentation.md  (Documentation)
    - setup_crispr_pipeline.sh          (This setup script)

EXAMPLES:
    # Setup in default directory
    $0

    # Setup in custom directory
    $0 -t /path/to/my/directory

    # Setup from different source
    $0 -s /path/to/source -t /path/to/target

EOF
}

# Function to check dependencies
check_dependencies() {
    print_info "Checking system dependencies..."
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is required but not found"
        print_info "Please install Python 3 before running this setup"
        exit 1
    else
        local python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
        print_info "Python 3 found: $python_version"
    fi
    
    # Check bash
    if ! command -v bash &> /dev/null; then
        print_error "Bash is required but not found"
        exit 1
    fi
    
    print_status "Dependencies check passed"
}

# Function to validate source files
validate_source_files() {
    print_info "Validating source files in: $SOURCE_DIR"
    
    local required_files=(
        "crispr_guide_design_final.py"
        "run_crispr_pipeline_final.sh"
        "CRISPR_Pipeline_Documentation.md"
    )
    
    local missing_files=()
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$SOURCE_DIR/$file" ]]; then
            missing_files+=("$file")
        else
            print_info "✓ Found: $file"
        fi
    done
    
    if [[ ${#missing_files[@]} -gt 0 ]]; then
        print_error "Missing required files:"
        for file in "${missing_files[@]}"; do
            print_error "  - $file"
        done
        print_info "Make sure all pipeline files are in the source directory"
        exit 1
    fi
    
    print_status "Source files validation passed"
}

# Function to create target directory
create_target_directory() {
    print_info "Setting up target directory: $TARGET_DIR"
    
    if [[ ! -d "$TARGET_DIR" ]]; then
        print_info "Creating directory: $TARGET_DIR"
        mkdir -p "$TARGET_DIR"
        print_status "Directory created successfully"
    else
        print_info "Directory already exists"
    fi
    
    # Check write permissions
    if [[ ! -w "$TARGET_DIR" ]]; then
        print_error "No write permission for directory: $TARGET_DIR"
        exit 1
    fi
    
    print_status "Target directory ready"
}

# Function to copy files
copy_files() {
    print_info "Copying pipeline files..."
    
    local files=(
        "crispr_guide_design_final.py"
        "run_crispr_pipeline_final.sh"
        "CRISPR_Pipeline_Documentation.md"
        "setup_crispr_pipeline.sh"
    )
    
    for file in "${files[@]}"; do
        if [[ -f "$SOURCE_DIR/$file" ]]; then
            cp "$SOURCE_DIR/$file" "$TARGET_DIR/"
            print_info "✓ Copied: $file"
        else
            print_warning "File not found, skipping: $file"
        fi
    done
    
    print_status "File copying completed"
}

# Function to set permissions
set_permissions() {
    print_info "Setting file permissions..."
    
    # Make scripts executable
    chmod +x "$TARGET_DIR/crispr_guide_design_final.py" 2>/dev/null || true
    chmod +x "$TARGET_DIR/run_crispr_pipeline_final.sh" 2>/dev/null || true
    chmod +x "$TARGET_DIR/setup_crispr_pipeline.sh" 2>/dev/null || true
    
    # Make documentation readable
    chmod 644 "$TARGET_DIR/CRISPR_Pipeline_Documentation.md" 2>/dev/null || true
    
    print_status "Permissions set successfully"
}

# Function to validate installation
validate_installation() {
    print_info "Validating installation..."
    
    cd "$TARGET_DIR"
    
    # Test Python script
    if python3 crispr_guide_design_final.py --help &>/dev/null; then
        print_info "✓ Python script validation passed"
    else
        print_warning "Python script validation failed"
    fi
    
    # Test bash script
    if ./run_crispr_pipeline_final.sh --help &>/dev/null; then
        print_info "✓ Bash script validation passed"
    else
        print_warning "Bash script validation failed"
    fi
    
    print_status "Installation validation completed"
}

# Function to check for pathdb.fna
check_pathdb() {
    print_info "Checking for pathdb.fna file..."
    
    cd "$TARGET_DIR"
    
    if [[ -f "pathdb.fna" ]]; then
        local seq_count=$(grep -c "^>" pathdb.fna 2>/dev/null || echo "0")
        local file_size=$(du -h pathdb.fna 2>/dev/null | cut -f1 || echo "unknown")
        
        print_status "Found pathdb.fna:"
        print_info "  Sequences: $seq_count"
        print_info "  File size: $file_size"
        
        # Quick format check
        if head -1 pathdb.fna | grep -q "^>"; then
            print_info "  Format: Valid FASTA"
        else
            print_warning "  Format: May not be valid FASTA"
        fi
        
        return 0
    else
        print_warning "pathdb.fna not found in target directory"
        print_info "You'll need to provide this file before running the pipeline"
        return 1
    fi
}

# Function to run test
run_test() {
    print_info "Running pipeline test..."
    
    cd "$TARGET_DIR"
    
    if [[ -f "pathdb.fna" ]]; then
        print_info "Testing with pathdb.fna (dry run)..."
        if ./run_crispr_pipeline_final.sh -i pathdb.fna -n 5 -v --help &>/dev/null; then
            print_status "Pipeline test passed"
        else
            print_warning "Pipeline test had issues"
        fi
    else
        print_info "Skipping test - pathdb.fna not available"
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--target)
            TARGET_DIR="$2"
            shift 2
            ;;
        -s|--source)
            SOURCE_DIR="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    print_status "=== CRISPR Guide RNA Design Pipeline Setup ==="
    print_info "Setting up CRISPR guide RNA design pipeline"
    print_info "Source directory: $SOURCE_DIR"
    print_info "Target directory: $TARGET_DIR"
    
    # Run setup steps
    check_dependencies
    validate_source_files
    create_target_directory
    copy_files
    set_permissions
    validate_installation
    
    # Check for pathdb.fna and run test
    if check_pathdb; then
        run_test
    fi
    
    # Final summary
    print_status "=== Setup Completed Successfully! ==="
    print_info "Pipeline installed in: $TARGET_DIR"
    print_info ""
    print_info "Next steps:"
    print_info "1. Navigate to the directory: cd $TARGET_DIR"
    print_info "2. Ensure pathdb.fna is present"
    print_info "3. Run the pipeline: ./run_crispr_pipeline_final.sh -i pathdb.fna"
    print_info "4. Check documentation: cat CRISPR_Pipeline_Documentation.md"
    print_info ""
    print_status "Ready for CRISPR guide RNA design!"
}

# Run main function
main "$@"
