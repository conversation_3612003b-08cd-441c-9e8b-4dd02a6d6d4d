#!/bin/bash

# CRISPR Guide RNA Design Pipeline - Final Version
# Specialized for designing spatially separated guide RNAs for CRISPR-Cas9 applications
# Author: Bioinformatics Pipeline for Viral Targeting

set -e  # Exit on any error

# Default parameters
INPUT_FASTA=""
OUTPUT_DIR="crispr_guides_final"
KMER_SIZE=23
CONSERVATION_THRESHOLD=0.5
GUIDE_LENGTH=20
MIN_SEPARATION=150
MAX_GUIDES=10
GC_MIN=20
GC_MAX=80

# Function to display usage
usage() {
    cat << 'USAGE_EOF'
CRISPR Guide RNA Design Pipeline - Final Version
===============================================

Usage: ./crispr_guide_pipeline_final.sh -i INPUT_FASTA [OPTIONS]

REQUIRED:
    -i, --input         Input FASTA file with multiple viral sequences

OPTIONS:
    -o, --output        Output directory (default: crispr_guides_final)
    -k, --kmer-size     K-mer size for conservation analysis (default: 23)
    -c, --conservation  Minimum conservation score 0-1 (default: 0.5)
    -l, --length        Guide RNA length (default: 20)
    -s, --separation    Minimum separation between guides in bp (default: 150)
    -n, --max-guides    Maximum number of guide RNAs (default: 10)
    --gc-min            Minimum GC content percentage (default: 20)
    --gc-max            Maximum GC content percentage (default: 80)
    -h, --help          Show this help message

EXAMPLES:
    # Design 10 high-quality guide RNAs for viral targeting
    ./crispr_guide_pipeline_final.sh -i viral_sequences.fasta
    
    # Design 50 guide RNAs with 200bp separation for comprehensive coverage
    ./crispr_guide_pipeline_final.sh -i sequences.fasta -n 50 -s 200 -c 0.8
    
    # Design guide RNAs for diverse viral sequences with relaxed parameters
    ./crispr_guide_pipeline_final.sh -i sequences.fasta -c 0.3 -s 100 --gc-min 30 --gc-max 70

OUTPUT FILES:
    - guide_rnas.csv: Detailed guide RNA information with positions and scores
    - guide_rnas.fasta: Guide RNA sequences in FASTA format for ordering
    - guide_rna_summary.txt: Summary statistics and top guide RNAs
    - conserved_regions.txt: Identified conserved regions analysis

NOTES:
    - Guide RNAs are designed from highly conserved regions across input sequences
    - Spatial separation ensures targeting of distinct genomic locations
    - PAM sites (NGG, NAG) are automatically identified for SpCas9 compatibility
    - GC content and melting temperature are calculated for each guide RNA

USAGE_EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -i|--input)
            INPUT_FASTA="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -k|--kmer-size)
            KMER_SIZE="$2"
            shift 2
            ;;
        -c|--conservation)
            CONSERVATION_THRESHOLD="$2"
            shift 2
            ;;
        -l|--length)
            GUIDE_LENGTH="$2"
            shift 2
            ;;
        -s|--separation)
            MIN_SEPARATION="$2"
            shift 2
            ;;
        -n|--max-guides)
            MAX_GUIDES="$2"
            shift 2
            ;;
        --gc-min)
            GC_MIN="$2"
            shift 2
            ;;
        --gc-max)
            GC_MAX="$2"
            shift 2
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Check required parameters
if [[ -z "$INPUT_FASTA" ]]; then
    echo "Error: Input FASTA file is required"
    usage
    exit 1
fi

if [[ ! -f "$INPUT_FASTA" ]]; then
    echo "Error: Input file '$INPUT_FASTA' not found"
    exit 1
fi

# Create output directory
mkdir -p "$OUTPUT_DIR"

echo "=== CRISPR GUIDE RNA DESIGN PIPELINE - FINAL VERSION ==="
echo "Input FASTA: $INPUT_FASTA"
echo "Output directory: $OUTPUT_DIR"
echo "K-mer size: $KMER_SIZE bp (includes PAM site region)"
echo "Conservation threshold: $CONSERVATION_THRESHOLD"
echo "Guide RNA length: $GUIDE_LENGTH bp"
echo "Minimum separation: $MIN_SEPARATION bp"
echo "Maximum guides: $MAX_GUIDES"
echo "GC content range: $GC_MIN-$GC_MAX%"
echo ""

# Check if Python is available
PYTHON_CMD="python3"
if ! command -v python3 &> /dev/null; then
    echo "Error: python3 not found"
    exit 1
fi

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Check if the simplified CRISPR design script exists
CRISPR_SCRIPT="$SCRIPT_DIR/simplified_crispr_design.py"
if [[ ! -f "$CRISPR_SCRIPT" ]]; then
    echo "Error: simplified_crispr_design.py not found in $SCRIPT_DIR"
    exit 1
fi

# Run the CRISPR guide RNA design
echo "Running CRISPR guide RNA design analysis..."
echo "This may take a few minutes depending on the number of sequences..."
echo ""

$PYTHON_CMD "$CRISPR_SCRIPT" \
    "$INPUT_FASTA" \
    -o "$OUTPUT_DIR" \
    -k "$KMER_SIZE" \
    -c "$CONSERVATION_THRESHOLD" \
    -l "$GUIDE_LENGTH" \
    -s "$MIN_SEPARATION" \
    -n "$MAX_GUIDES" \
    --gc-min "$GC_MIN" \
    --gc-max "$GC_MAX"

if [[ $? -eq 0 ]]; then
    echo ""
    echo "=== CRISPR GUIDE RNA DESIGN COMPLETED SUCCESSFULLY ==="
    echo "Results saved in: $OUTPUT_DIR"
    echo ""
    echo "Output files:"
    echo "  - ${OUTPUT_DIR}/guide_rnas.csv: Detailed guide RNA information"
    echo "  - ${OUTPUT_DIR}/guide_rnas.fasta: Guide RNA sequences for ordering"
    echo "  - ${OUTPUT_DIR}/guide_rna_summary.txt: Summary statistics"
    echo "  - ${OUTPUT_DIR}/conserved_regions.txt: Conserved regions analysis"
    echo ""
    
    if [[ -f "${OUTPUT_DIR}/guide_rna_summary.txt" ]]; then
        echo "=== SUMMARY ==="
        cat "${OUTPUT_DIR}/guide_rna_summary.txt"
    fi
    
    echo ""
    echo "=== NEXT STEPS FOR CRISPR EXPERIMENTS ==="
    echo "1. Review guide RNA sequences in ${OUTPUT_DIR}/guide_rnas.fasta"
    echo "2. Order guide RNA sequences for cloning into your CRISPR vector"
    echo "3. Validate PAM sites and check for potential off-targets using tools like:"
    echo "   - CRISPOR (http://crispor.tefor.net/)"
    echo "   - Cas-OFFinder (http://www.rgenome.net/cas-offinder/)"
    echo "4. Test guide RNAs in your experimental system"
    echo "5. The spatially separated design ensures broad genomic coverage"
    
else
    echo "Error: CRISPR guide RNA design failed"
    exit 1
fi
