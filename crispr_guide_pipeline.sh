#!/bin/bash

# CRISPR Guide RNA Design Pipeline
# Specialized for designing spatially separated guide RNAs for CRISPR-Cas9 applications
# Based on ppdesign methodology with CRISPR-specific optimizations

set -e  # Exit on any error

# Default parameters
INPUT_FASTA=""
OUTPUT_DIR="crispr_guides"
KMER_SIZE=23
CONSERVATION_THRESHOLD=0.8
GUIDE_LENGTH=20
MIN_SEPARATION=100
MAX_GUIDES=50
GC_MIN=20
GC_MAX=80
THREADS=8

# Function to display usage
usage() {
    cat << 'USAGE_EOF'
CRISPR Guide RNA Design Pipeline
===============================

Usage: ./crispr_guide_pipeline.sh -i INPUT_FASTA [OPTIONS]

REQUIRED:
    -i, --input         Input FASTA file with multiple sequences

OPTIONS:
    -o, --output        Output directory (default: crispr_guides)
    -k, --kmer-size     K-mer size for conservation analysis (default: 23)
    -c, --conservation  Minimum conservation score 0-1 (default: 0.8)
    -l, --length        Guide RNA length (default: 20)
    -s, --separation    Minimum separation between guides in bp (default: 100)
    -n, --max-guides    Maximum number of guide RNAs (default: 50)
    --gc-min            Minimum GC content % (default: 20)
    --gc-max            Maximum GC content % (default: 80)
    -t, --threads       Number of threads (default: 8)
    -h, --help          Show this help message

EXAMPLES:
    # Basic usage - design up to 50 guide RNAs
    ./crispr_guide_pipeline.sh -i viral_sequences.fna
    
    # Design 10 high-quality guide RNAs with 200bp separation
    ./crispr_guide_pipeline.sh -i sequences.fna -n 10 -s 200 -c 0.9
    
    # Custom parameters for diverse sequences
    ./crispr_guide_pipeline.sh -i sequences.fna -c 0.6 -s 150 --gc-min 30 --gc-max 70

CRISPR-SPECIFIC FEATURES:
    - PAM site detection (NGG for SpCas9)
    - Spatial separation enforcement
    - Off-target risk assessment
    - Both forward and reverse strand analysis
    - CRISPR-optimized scoring

OUTPUT FILES:
    - guide_rnas.csv: Detailed guide RNA information
    - guide_rnas.fasta: Guide RNA sequences in FASTA format
    - guide_rna_summary.txt: Summary statistics
    - conserved_regions.txt: Identified conserved regions

USAGE_EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -i|--input)
            INPUT_FASTA="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -k|--kmer-size)
            KMER_SIZE="$2"
            shift 2
            ;;
        -c|--conservation)
            CONSERVATION_THRESHOLD="$2"
            shift 2
            ;;
        -l|--length)
            GUIDE_LENGTH="$2"
            shift 2
            ;;
        -s|--separation)
            MIN_SEPARATION="$2"
            shift 2
            ;;
        -n|--max-guides)
            MAX_GUIDES="$2"
            shift 2
            ;;
        --gc-min)
            GC_MIN="$2"
            shift 2
            ;;
        --gc-max)
            GC_MAX="$2"
            shift 2
            ;;
        -t|--threads)
            THREADS="$2"
            shift 2
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Check required parameters
if [[ -z "$INPUT_FASTA" ]]; then
    echo "Error: Input FASTA file is required"
    usage
    exit 1
fi

if [[ ! -f "$INPUT_FASTA" ]]; then
    echo "Error: Input file '$INPUT_FASTA' not found"
    exit 1
fi

# Create output directory
mkdir -p "$OUTPUT_DIR"

echo "=== CRISPR GUIDE RNA DESIGN PIPELINE ==="
echo "Input FASTA: $INPUT_FASTA"
echo "Output directory: $OUTPUT_DIR"
echo "K-mer size: $KMER_SIZE bp (includes PAM site)"
echo "Conservation threshold: $CONSERVATION_THRESHOLD"
echo "Guide RNA length: $GUIDE_LENGTH bp"
echo "Minimum separation: $MIN_SEPARATION bp"
echo "Maximum guides: $MAX_GUIDES"
echo "GC content range: $GC_MIN-$GC_MAX%"
echo "Threads: $THREADS"
echo ""

# Check if Python environment is available
PYTHON_CMD="python3"
if [[ -f "../env/bin/python" ]]; then
    PYTHON_CMD="../env/bin/python"
    echo "Using virtual environment: ../env/bin/python"
elif ! command -v python3 &> /dev/null; then
    echo "Error: python3 not found"
    exit 1
fi

# Check required Python packages
$PYTHON_CMD -c "
import sys
try:
    import Bio, pandas, numpy, primer3
    print('All required Python packages are available')
except ImportError as e:
    print(f'Missing Python package: {e}')
    print('Please install: pip install biopython pandas numpy primer3-py tqdm')
    sys.exit(1)
"

if [[ $? -ne 0 ]]; then
    exit 1
fi

# Run the CRISPR guide RNA design
echo "Running CRISPR guide RNA design analysis..."
$PYTHON_CMD crispr_grna_design.py \
    "$INPUT_FASTA" \
    -o "$OUTPUT_DIR" \
    -k "$KMER_SIZE" \
    -c "$CONSERVATION_THRESHOLD" \
    -l "$GUIDE_LENGTH" \
    -s "$MIN_SEPARATION" \
    -n "$MAX_GUIDES" \
    --gc-min "$GC_MIN" \
    --gc-max "$GC_MAX"

if [[ $? -eq 0 ]]; then
    echo ""
    echo "=== CRISPR GUIDE RNA DESIGN COMPLETED SUCCESSFULLY ==="
    echo "Results saved in: $OUTPUT_DIR"
    echo ""
    echo "Output files:"
    echo "  - ${OUTPUT_DIR}/guide_rnas.csv: Detailed guide RNA information"
    echo "  - ${OUTPUT_DIR}/guide_rnas.fasta: Guide RNA sequences in FASTA format"
    echo "  - ${OUTPUT_DIR}/guide_rna_summary.txt: Summary statistics"
    echo "  - ${OUTPUT_DIR}/conserved_regions.txt: Identified conserved regions"
    echo ""
    
    if [[ -f "${OUTPUT_DIR}/guide_rna_summary.txt" ]]; then
        echo "Summary:"
        cat "${OUTPUT_DIR}/guide_rna_summary.txt"
    fi
    
    echo ""
    echo "=== NEXT STEPS FOR CRISPR EXPERIMENTS ==="
    echo "1. Review guide RNA sequences in ${OUTPUT_DIR}/guide_rnas.fasta"
    echo "2. Validate PAM sites and check for off-targets using tools like:"
    echo "   - CRISPOR (http://crispor.tefor.net/)"
    echo "   - Cas-OFFinder"
    echo "   - CHOPCHOP"
    echo "3. Order guide RNA sequences for cloning into your CRISPR vector"
    echo "4. Test guide RNAs in your experimental system"
    
else
    echo "Error: CRISPR guide RNA design failed"
    exit 1
fi
