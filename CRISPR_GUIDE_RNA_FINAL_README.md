# CRISPR Guide RNA Design Pipeline - Final Version

## Overview

This pipeline is specifically designed for senior bioinformaticians to design high-quality CRISPR guide RNAs (gRNAs) from conserved regions in viral sequences. The pipeline ensures spatial separation between guide RNAs to target distinct genomic locations, making it ideal for comprehensive CRISPR-Cas9 targeting strategies.

## Key Features

- **Conserved Region Detection**: Uses k-mer analysis to identify highly conserved regions across multiple viral sequences
- **Spatial Separation**: Enforces minimum distance between guide RNAs (default: 150bp) to ensure targeting of distinct genomic regions
- **PAM Site Recognition**: Automatically identifies SpCas9 PAM sites (NGG, NAG) for guide RNA design
- **Quality Filtering**: Filters guide RNAs based on GC content and other quality metrics
- **Comprehensive Output**: Provides detailed analysis including conservation scores, melting temperatures, and genomic positions

## Files

1. **`simplified_crispr_design.py`** - Main Python script for guide RNA design
2. **`crispr_guide_pipeline_final.sh`** - Bash wrapper script for easy execution
3. **`CRISPR_GUIDE_RNA_FINAL_README.md`** - This documentation file

## Requirements

- Python 3.6+
- No additional Python packages required (uses only standard library)
- Unix/Linux environment with bash

## Quick Start

### Basic Usage (10 guide RNAs)
```bash
./crispr_guide_pipeline_final.sh -i your_viral_sequences.fasta
```

### Advanced Usage (50 guide RNAs with custom parameters)
```bash
./crispr_guide_pipeline_final.sh -i viral_sequences.fasta -n 50 -s 200 -c 0.8 -o my_guides
```

## Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `-i, --input` | Input FASTA file with multiple viral sequences | **Required** |
| `-o, --output` | Output directory | `crispr_guides_final` |
| `-n, --max-guides` | Maximum number of guide RNAs | `10` |
| `-s, --separation` | Minimum separation between guides (bp) | `150` |
| `-c, --conservation` | Minimum conservation score (0-1) | `0.5` |
| `-k, --kmer-size` | K-mer size for conservation analysis | `23` |
| `-l, --length` | Guide RNA length | `20` |
| `--gc-min` | Minimum GC content percentage | `20` |
| `--gc-max` | Maximum GC content percentage | `80` |

## Output Files

### 1. `guide_rnas.fasta`
FASTA format file with guide RNA sequences ready for ordering:
```
>gRNA_1 PAM:TGG GC:35.0% Tm:54.0C Cons:1.000 forward
GATTATATGGGTTTTTTCGC
>gRNA_2 PAM:CGG GC:35.0% Tm:58.0C Cons:0.405 forward
CGTAATAGGGATCGTTTATT
```

### 2. `guide_rnas.csv`
Detailed information for each guide RNA:
```csv
guide_id,sequence,pam_sequence,length,gc_content,tm,conservation_score,position,strand
gRNA_1,GATTATATGGGTTTTTTCGC,TGG,20,35.0,54.0,1.000,1234,forward
```

### 3. `guide_rna_summary.txt`
Summary statistics and top guide RNAs ranked by conservation score

### 4. `conserved_regions.txt`
Detailed analysis of identified conserved regions

## Example Workflows

### For Viral Family Analysis
```bash
# Combine sequences from a viral family
cat viral_family/*.fasta > combined_viral_family.fasta

# Design 10 high-quality guide RNAs
./crispr_guide_pipeline_final.sh -i combined_viral_family.fasta -n 10 -c 0.8

# Design 50 guide RNAs for comprehensive coverage
./crispr_guide_pipeline_final.sh -i combined_viral_family.fasta -n 50 -s 200 -c 0.6
```

### For Diverse Viral Sequences
```bash
# Use relaxed parameters for diverse sequences
./crispr_guide_pipeline_final.sh -i diverse_viruses.fasta -c 0.3 -s 100 --gc-min 30 --gc-max 70
```

## Understanding the Results

### Conservation Score
- **1.0**: Guide RNA sequence found in ALL input sequences (perfect conservation)
- **0.8**: Guide RNA sequence found in 80% of input sequences
- **0.5**: Guide RNA sequence found in 50% of input sequences

### Spatial Separation
- Guide RNAs are selected to be at least 150bp apart (default)
- This ensures targeting of distinct genomic regions
- Prevents clustering of guide RNAs in a single region

### PAM Sites
- **NGG**: Canonical SpCas9 PAM site (most efficient)
- **NAG**: Alternative SpCas9 PAM site (lower efficiency)
- PAM sites are automatically detected and reported

## Validation and Next Steps

1. **Review Output**: Check `guide_rna_summary.txt` for overview
2. **Sequence Validation**: Review sequences in `guide_rnas.fasta`
3. **Off-target Analysis**: Use tools like CRISPOR or Cas-OFFinder
4. **Experimental Testing**: Order and test guide RNAs in your system

## Recommended Parameters by Use Case

### High Specificity (Closely Related Sequences)
```bash
./crispr_guide_pipeline_final.sh -i sequences.fasta -n 10 -c 0.9 -s 200
```

### Broad Coverage (Diverse Sequences)
```bash
./crispr_guide_pipeline_final.sh -i sequences.fasta -n 50 -c 0.5 -s 150
```

### Exploratory Analysis (Many Candidates)
```bash
./crispr_guide_pipeline_final.sh -i sequences.fasta -n 100 -c 0.3 -s 100
```

## Troubleshooting

### No Guide RNAs Found
- Lower conservation threshold: `-c 0.3` or `-c 0.1`
- Reduce minimum separation: `-s 100` or `-s 50`
- Check input sequences for quality and diversity

### Too Few Guide RNAs
- Increase maximum guides: `-n 50` or `-n 100`
- Lower conservation threshold: `-c 0.4`
- Adjust GC content range: `--gc-min 15 --gc-max 85`

### Performance Issues
- Large datasets may take several minutes
- Consider splitting very large FASTA files
- Monitor memory usage for datasets with >1000 sequences

## Technical Notes

- Uses k-mer based conservation analysis for efficiency
- Implements spatial separation algorithm to prevent clustering
- Calculates melting temperature using simple formula: Tm = 2*(A+T) + 4*(G+C)
- Supports both forward and reverse strand guide RNAs
- Automatically handles sequence quality filtering

## Contact and Support

This pipeline was designed for viral CRISPR targeting applications. For questions or issues, refer to the code comments in `simplified_crispr_design.py` for detailed implementation information.

---

**Note**: Always validate guide RNAs experimentally and check for off-target effects before use in critical applications.
