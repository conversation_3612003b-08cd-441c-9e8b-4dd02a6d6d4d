#!/usr/bin/env python3
"""
Simplified CRISPR Guide RNA Design for Conserved Regions
Designed for Python 3.6+ with minimal dependencies

This script designs 20bp guide RNAs from conserved regions with enforced spatial separation
to ensure effective targeting across different genomic regions for CRISPR-Cas9 applications.
"""

import os
import sys
import argparse
from pathlib import Path
from collections import defaultdict, Counter
import logging
from typing import Dict, List, Tuple, Set, Optional
import re

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ConservedRegion:
    """Represents a conserved region in multiple sequences."""
    
    def __init__(self, start: int, end: int, consensus: str, conservation_score: float):
        self.start = start
        self.end = end
        self.consensus = consensus
        self.conservation_score = conservation_score
        self.length = end - start + 1
    
    def __repr__(self):
        return f"ConservedRegion({self.start}-{self.end}, score={self.conservation_score:.3f}, seq={self.consensus[:20]}...)"

class GuideRNA:
    """Represents a CRISPR guide RNA."""
    
    def __init__(self, sequence: str, pam: str, position: int, strand: str, conservation_score: float):
        self.sequence = sequence
        self.pam = pam
        self.position = position
        self.strand = strand
        self.conservation_score = conservation_score
        self.gc_content = self.calculate_gc_content()
        self.tm = self.estimate_tm()
    
    def calculate_gc_content(self) -> float:
        """Calculate GC content percentage."""
        gc_count = self.sequence.count('G') + self.sequence.count('C')
        return (gc_count / len(self.sequence)) * 100
    
    def estimate_tm(self) -> float:
        """Estimate melting temperature using simple formula."""
        # Simple Tm estimation: Tm = 2*(A+T) + 4*(G+C)
        at_count = self.sequence.count('A') + self.sequence.count('T')
        gc_count = self.sequence.count('G') + self.sequence.count('C')
        return 2 * at_count + 4 * gc_count
    
    def __repr__(self):
        return f"GuideRNA({self.sequence}, PAM:{self.pam}, GC:{self.gc_content:.1f}%, Tm:{self.tm:.1f}°C)"

class SimpleCRISPRDesigner:
    """Simplified CRISPR guide RNA designer."""
    
    def __init__(self, guide_length: int = 20, min_separation: int = 100, 
                 gc_min: float = 20, gc_max: float = 80):
        self.guide_length = guide_length
        self.min_separation = min_separation
        self.gc_min = gc_min
        self.gc_max = gc_max
        self.pam_patterns = ['NGG', 'NAG']  # SpCas9 PAM sites
    
    def read_fasta(self, fasta_file: str) -> Dict[str, str]:
        """Read FASTA file and return sequences."""
        sequences = {}
        current_id = None
        current_seq = []
        
        with open(fasta_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith('>'):
                    if current_id:
                        sequences[current_id] = ''.join(current_seq).upper()
                    current_id = line[1:].split()[0]  # Take first part of header
                    current_seq = []
                elif line:
                    current_seq.append(line)
        
        if current_id:
            sequences[current_id] = ''.join(current_seq).upper()
        
        return sequences
    
    def find_conserved_regions(self, sequences: Dict[str, str], 
                             kmer_size: int = 23, conservation_threshold: float = 0.8) -> List[ConservedRegion]:
        """Find conserved regions using k-mer analysis."""
        logger.info(f"Finding conserved regions with k-mer size {kmer_size}")
        
        # Get all k-mers from all sequences
        kmer_positions = defaultdict(list)
        seq_names = list(sequences.keys())
        
        for seq_name, sequence in sequences.items():
            for i in range(len(sequence) - kmer_size + 1):
                kmer = sequence[i:i + kmer_size]
                if 'N' not in kmer:  # Skip k-mers with ambiguous bases
                    kmer_positions[kmer].append((seq_name, i))
        
        # Find conserved k-mers
        conserved_regions = []
        num_sequences = len(sequences)
        
        for kmer, positions in kmer_positions.items():
            # Calculate conservation score
            unique_sequences = set(pos[0] for pos in positions)
            conservation_score = len(unique_sequences) / num_sequences
            
            if conservation_score >= conservation_threshold:
                # Use the first position as representative
                start_pos = positions[0][1]
                end_pos = start_pos + kmer_size - 1
                
                region = ConservedRegion(start_pos, end_pos, kmer, conservation_score)
                conserved_regions.append(region)
        
        # Sort by conservation score
        conserved_regions.sort(key=lambda x: x.conservation_score, reverse=True)
        
        logger.info(f"Found {len(conserved_regions)} conserved regions")
        return conserved_regions
    
    def find_pam_sites(self, sequence: str) -> List[Tuple[int, str, str]]:
        """Find PAM sites in sequence. Returns (position, pam_sequence, strand)."""
        pam_sites = []
        
        # Forward strand PAM sites (NGG, NAG)
        for i in range(len(sequence) - 2):
            triplet = sequence[i:i+3]
            if (triplet[1:] == 'GG' and triplet[0] in 'ATCG') or \
               (triplet[1:] == 'AG' and triplet[0] in 'ATCG'):
                pam_sites.append((i, triplet, 'forward'))
        
        # Reverse strand PAM sites (CCN, CTN)
        for i in range(len(sequence) - 2):
            triplet = sequence[i:i+3]
            if (triplet[:2] == 'CC' and triplet[2] in 'ATCG') or \
               (triplet[:2] == 'CT' and triplet[2] in 'ATCG'):
                pam_sites.append((i, triplet, 'reverse'))
        
        return pam_sites
    
    def extract_guide_rnas(self, conserved_regions: List[ConservedRegion], 
                          sequences: Dict[str, str]) -> List[GuideRNA]:
        """Extract potential guide RNAs from conserved regions."""
        guide_rnas = []
        
        # Use the first sequence as reference for PAM site detection
        reference_seq = list(sequences.values())[0]
        
        for region in conserved_regions:
            # Extend region to look for PAM sites
            extended_start = max(0, region.start - 50)
            extended_end = min(len(reference_seq), region.end + 50)
            extended_sequence = reference_seq[extended_start:extended_end]
            
            # Find PAM sites in extended region
            pam_sites = self.find_pam_sites(extended_sequence)
            
            for pam_pos, pam_seq, strand in pam_sites:
                actual_pam_pos = extended_start + pam_pos
                
                if strand == 'forward':
                    # Guide RNA is upstream of PAM
                    guide_start = actual_pam_pos - self.guide_length
                    guide_end = actual_pam_pos
                else:
                    # Guide RNA is downstream of PAM (reverse complement)
                    guide_start = actual_pam_pos + 3
                    guide_end = actual_pam_pos + 3 + self.guide_length
                
                # Check if guide RNA is within bounds
                if guide_start >= 0 and guide_end <= len(reference_seq):
                    guide_seq = reference_seq[guide_start:guide_end]
                    
                    if strand == 'reverse':
                        guide_seq = self.reverse_complement(guide_seq)
                    
                    # Create guide RNA object
                    guide_rna = GuideRNA(guide_seq, pam_seq, guide_start, strand, region.conservation_score)
                    
                    # Filter by GC content
                    if self.gc_min <= guide_rna.gc_content <= self.gc_max:
                        guide_rnas.append(guide_rna)
        
        return guide_rnas
    
    def reverse_complement(self, sequence: str) -> str:
        """Return reverse complement of DNA sequence."""
        complement = {'A': 'T', 'T': 'A', 'G': 'C', 'C': 'G', 'N': 'N'}
        return ''.join(complement.get(base, base) for base in reversed(sequence))
    
    def select_spatially_separated_guides(self, guide_rnas: List[GuideRNA], 
                                        max_guides: int = 50) -> List[GuideRNA]:
        """Select spatially separated guide RNAs."""
        if not guide_rnas:
            return []
        
        # Sort by conservation score
        sorted_guides = sorted(guide_rnas, key=lambda x: x.conservation_score, reverse=True)
        
        selected_guides = []
        selected_positions = []
        
        for guide in sorted_guides:
            if len(selected_guides) >= max_guides:
                break
            
            # Check spatial separation
            is_separated = True
            for pos in selected_positions:
                if abs(guide.position - pos) < self.min_separation:
                    is_separated = False
                    break
            
            if is_separated:
                selected_guides.append(guide)
                selected_positions.append(guide.position)
        
        logger.info(f"Selected {len(selected_guides)} spatially separated guide RNAs")
        return selected_guides
    
    def design_guides(self, fasta_file: str, output_dir: str, 
                     kmer_size: int = 23, conservation_threshold: float = 0.8,
                     max_guides: int = 50) -> List[GuideRNA]:
        """Main method to design guide RNAs."""
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Read sequences
        logger.info(f"Reading sequences from {fasta_file}")
        sequences = self.read_fasta(fasta_file)
        logger.info(f"Loaded {len(sequences)} sequences")
        
        if len(sequences) < 2:
            logger.warning("Need at least 2 sequences for conservation analysis")
            return []
        
        # Find conserved regions
        conserved_regions = self.find_conserved_regions(sequences, kmer_size, conservation_threshold)
        
        if not conserved_regions:
            logger.warning("No conserved regions found")
            return []
        
        # Extract guide RNAs
        logger.info("Extracting potential guide RNAs")
        guide_rnas = self.extract_guide_rnas(conserved_regions, sequences)
        logger.info(f"Found {len(guide_rnas)} potential guide RNAs")
        
        # Select spatially separated guides
        selected_guides = self.select_spatially_separated_guides(guide_rnas, max_guides)
        
        # Save results
        self.save_results(selected_guides, conserved_regions, output_dir)
        
        return selected_guides

    def save_results(self, guide_rnas: List[GuideRNA], conserved_regions: List[ConservedRegion],
                    output_dir: str):
        """Save results to files."""

        # Save guide RNAs to CSV
        csv_file = os.path.join(output_dir, 'guide_rnas.csv')
        with open(csv_file, 'w') as f:
            f.write('guide_id,sequence,pam_sequence,length,gc_content,tm,conservation_score,position,strand\n')
            for i, guide in enumerate(guide_rnas, 1):
                f.write(f'gRNA_{i},{guide.sequence},{guide.pam},{len(guide.sequence)},'
                       f'{guide.gc_content:.1f},{guide.tm:.1f},{guide.conservation_score:.3f},'
                       f'{guide.position},{guide.strand}\n')

        # Save guide RNAs to FASTA
        fasta_file = os.path.join(output_dir, 'guide_rnas.fasta')
        with open(fasta_file, 'w') as f:
            for i, guide in enumerate(guide_rnas, 1):
                f.write(f'>gRNA_{i} PAM:{guide.pam} GC:{guide.gc_content:.1f}% '
                       f'Tm:{guide.tm:.1f}C Cons:{guide.conservation_score:.3f} {guide.strand}\n')
                f.write(f'{guide.sequence}\n')

        # Save summary
        summary_file = os.path.join(output_dir, 'guide_rna_summary.txt')
        with open(summary_file, 'w') as f:
            f.write('CRISPR GUIDE RNA DESIGN SUMMARY\n')
            f.write('=' * 50 + '\n')
            f.write(f'Total conserved regions found: {len(conserved_regions)}\n')
            f.write(f'Total guide RNAs designed: {len(guide_rnas)}\n\n')

            if guide_rnas:
                avg_gc = sum(g.gc_content for g in guide_rnas) / len(guide_rnas)
                avg_tm = sum(g.tm for g in guide_rnas) / len(guide_rnas)
                avg_cons = sum(g.conservation_score for g in guide_rnas) / len(guide_rnas)

                strand_counts = Counter(g.strand for g in guide_rnas)

                f.write('Guide RNA Statistics:\n')
                f.write(f'  Average GC content: {avg_gc:.1f}%\n')
                f.write(f'  Average Tm: {avg_tm:.1f}°C\n')
                f.write(f'  Average conservation score: {avg_cons:.3f}\n')
                f.write(f'  Strand distribution: Forward={strand_counts["forward"]}, '
                       f'Reverse={strand_counts["reverse"]}\n')

                if len(guide_rnas) > 1:
                    positions = [g.position for g in guide_rnas]
                    min_sep = min(abs(positions[i] - positions[j])
                                for i in range(len(positions))
                                for j in range(i+1, len(positions)))
                    f.write(f'  Minimum separation achieved: {min_sep} bp\n')

                f.write(f'\nTop {min(10, len(guide_rnas))} guide RNAs by conservation score:\n')
                for i, guide in enumerate(guide_rnas[:10], 1):
                    f.write(f'  gRNA_{i}: {guide.sequence} (PAM:{guide.pam}, '
                           f'GC:{guide.gc_content:.1f}%, Cons:{guide.conservation_score:.3f}, {guide.strand})\n')

        # Save conserved regions
        regions_file = os.path.join(output_dir, 'conserved_regions.txt')
        with open(regions_file, 'w') as f:
            f.write('CONSERVED REGIONS ANALYSIS\n')
            f.write('=' * 50 + '\n')
            for i, region in enumerate(conserved_regions[:100], 1):  # Top 100 regions
                f.write(f'Region {i}: {region.start}-{region.end} '
                       f'(score: {region.conservation_score:.3f})\n')
                f.write(f'  Sequence: {region.consensus}\n\n')

        logger.info(f"Results saved to {output_dir}")

def main():
    parser = argparse.ArgumentParser(description='Simplified CRISPR Guide RNA Design')
    parser.add_argument('input_fasta', help='Input FASTA file with multiple sequences')
    parser.add_argument('-o', '--output', default='crispr_guides', help='Output directory')
    parser.add_argument('-k', '--kmer-size', type=int, default=23, help='K-mer size for conservation analysis')
    parser.add_argument('-c', '--conservation', type=float, default=0.8, help='Minimum conservation score')
    parser.add_argument('-l', '--length', type=int, default=20, help='Guide RNA length')
    parser.add_argument('-s', '--separation', type=int, default=100, help='Minimum separation between guides')
    parser.add_argument('-n', '--max-guides', type=int, default=50, help='Maximum number of guide RNAs')
    parser.add_argument('--gc-min', type=float, default=20, help='Minimum GC content percentage')
    parser.add_argument('--gc-max', type=float, default=80, help='Maximum GC content percentage')

    args = parser.parse_args()

    # Create designer
    designer = SimpleCRISPRDesigner(
        guide_length=args.length,
        min_separation=args.separation,
        gc_min=args.gc_min,
        gc_max=args.gc_max
    )

    # Design guide RNAs
    guide_rnas = designer.design_guides(
        args.input_fasta,
        args.output,
        args.kmer_size,
        args.conservation,
        args.max_guides
    )

    if guide_rnas:
        print(f"\nSuccessfully designed {len(guide_rnas)} guide RNAs!")
        print(f"Results saved in: {args.output}")
    else:
        print("No guide RNAs could be designed with the given parameters.")

if __name__ == '__main__':
    main()
