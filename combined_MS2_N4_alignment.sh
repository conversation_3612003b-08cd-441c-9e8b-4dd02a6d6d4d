#!/bin/bash

# Combined Read Alignment Workflow: MS2_N4 Reference with FSEM002C & FSEM001O Reads
# Topic: High-Coverage Viral Genome Alignment for IGV Visualization
# 
# This script aligns two large FASTQ files to the MS2_N4 reference genome,
# combining reads to maximize mapped coverage for comprehensive genome analysis.
# Designed for IGV visualization with proper indexing and coverage metrics.

set -euo pipefail  # Exit on error, undefined variables, and pipe failures

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

print_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

print_info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Define file paths
WORK_DIR="/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/bwa-env"
REFERENCE="/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/MS2_N4.fna"
READ_DIR="/clusterfs/jgi/scratch/science/mgs/nelli/databases/ww/oakland/00data/readsf_10M"
READ1="${READ_DIR}/FSEM002C_S40_L007.anqdpht.fastq.gz"
READ2="${READ_DIR}/FSEM001O_S20_L008.anqdpht.fastq.gz"

# Output files
OUTPUT_PREFIX="MS2_N4_combined_FSEM002C_FSEM001O"
SAM_FILE="${OUTPUT_PREFIX}.sam"
BAM_FILE="${OUTPUT_PREFIX}.bam"
SORTED_BAM="${OUTPUT_PREFIX}.sorted.bam"
STATS_FILE="${OUTPUT_PREFIX}_stats.txt"
COVERAGE_FILE="${OUTPUT_PREFIX}_coverage.txt"

print_status "=== Combined Read Alignment Workflow Started ==="
print_info "Reference: MS2_N4.fna"
print_info "Read File 1: FSEM002C_S40_L007.anqdpht.fastq.gz"
print_info "Read File 2: FSEM001O_S20_L008.anqdpht.fastq.gz"
print_info "Working Directory: $WORK_DIR"

# Change to working directory
cd "$WORK_DIR"

# Step 1: Verify all input files exist
print_status "Step 1: Verifying input files..."
for file in "$REFERENCE" "$READ1" "$READ2"; do
    if [[ ! -f "$file" ]]; then
        print_error "Required file not found: $file"
        exit 1
    fi
    print_info "✓ Found: $(basename "$file") ($(du -h "$file" | cut -f1))"
done

# Step 2: Check if reference is already indexed
print_status "Step 2: Checking BWA index for reference genome..."
if [[ ! -f "${REFERENCE}.bwt" ]]; then
    print_warning "BWA index not found. Creating index for MS2_N4.fna..."
    pixi run bwa-mem2 index "$REFERENCE"
    print_status "✓ BWA index created successfully"
else
    print_info "✓ BWA index already exists"
fi

# Step 3: Perform separate alignments and merge
print_status "Step 3: Aligning reads to MS2_N4 reference..."
print_info "Using BWA-MEM2 for high-performance alignment"
print_info "Processing each read file separately as single-end reads"

# Define intermediate files
SAM1_FILE="${OUTPUT_PREFIX}_FSEM002C.sam"
SAM2_FILE="${OUTPUT_PREFIX}_FSEM001O.sam"

# Align first read file
print_info "Aligning FSEM002C_S40_L007.anqdpht.fastq.gz..."
pixi run bwa-mem2 mem -t 8 -M "$REFERENCE" "$READ1" > "$SAM1_FILE"
print_status "✓ First alignment completed: $SAM1_FILE"

# Align second read file
print_info "Aligning FSEM001O_S20_L008.anqdpht.fastq.gz..."
pixi run bwa-mem2 mem -t 8 -M "$REFERENCE" "$READ2" > "$SAM2_FILE"
print_status "✓ Second alignment completed: $SAM2_FILE"

# Merge SAM files (keeping header from first file only)
print_info "Merging SAM files to create combined alignment..."
head -n 1000 "$SAM1_FILE" | grep "^@" > "$SAM_FILE"  # Extract header
grep -v "^@" "$SAM1_FILE" >> "$SAM_FILE"  # Add alignments from first file
grep -v "^@" "$SAM2_FILE" >> "$SAM_FILE"  # Add alignments from second file

print_status "✓ Combined alignment created: $SAM_FILE"
print_info "Combined SAM file size: $(du -h "$SAM_FILE" | cut -f1)"

# Step 4: Convert SAM to BAM
print_status "Step 4: Converting SAM to BAM format..."
pixi run samtools view -bS "$SAM_FILE" > "$BAM_FILE"
print_status "✓ BAM conversion completed: $BAM_FILE"

# Step 5: Sort BAM file
print_status "Step 5: Sorting BAM file by coordinates..."
pixi run samtools sort "$BAM_FILE" -o "$SORTED_BAM"
print_status "✓ BAM sorting completed: $SORTED_BAM"

# Step 6: Index the sorted BAM file
print_status "Step 6: Creating BAM index for IGV compatibility..."
pixi run samtools index "$SORTED_BAM"
print_status "✓ BAM indexing completed: ${SORTED_BAM}.bai"

# Step 7: Generate alignment statistics
print_status "Step 7: Generating alignment statistics..."
{
    echo "=== MS2_N4 Combined Alignment Statistics ==="
    echo "Generated on: $(date)"
    echo "Reference: MS2_N4.fna"
    echo "Read Files: FSEM002C_S40_L007 + FSEM001O_S20_L008"
    echo ""
    echo "=== File Sizes ==="
    echo "Reference: $(du -h "$REFERENCE" | cut -f1)"
    echo "Read 1: $(du -h "$READ1" | cut -f1)"
    echo "Read 2: $(du -h "$READ2" | cut -f1)"
    echo "Final BAM: $(du -h "$SORTED_BAM" | cut -f1)"
    echo ""
    echo "=== Alignment Statistics ==="
    pixi run samtools flagstat "$SORTED_BAM"
    echo ""
    echo "=== Reference Information ==="
    pixi run samtools view -H "$SORTED_BAM" | grep "^@SQ"
} > "$STATS_FILE"

print_status "✓ Statistics saved to: $STATS_FILE"

# Step 8: Generate coverage analysis
print_status "Step 8: Generating coverage analysis..."
pixi run samtools depth "$SORTED_BAM" > "$COVERAGE_FILE"
print_status "✓ Coverage analysis saved to: $COVERAGE_FILE"

# Step 9: Calculate coverage statistics
print_status "Step 9: Calculating coverage statistics..."
{
    echo ""
    echo "=== Coverage Statistics ==="
    echo "Mean coverage: $(awk '{sum+=$3; count++} END {printf "%.2f\n", sum/count}' "$COVERAGE_FILE")"
    echo "Max coverage: $(awk 'BEGIN{max=0} {if($3>max) max=$3} END{print max}' "$COVERAGE_FILE")"
    echo "Positions with coverage > 0: $(awk '$3>0' "$COVERAGE_FILE" | wc -l)"
    echo "Total positions: $(wc -l < "$COVERAGE_FILE")"
    echo "Coverage percentage: $(awk '$3>0' "$COVERAGE_FILE" | wc -l | awk -v total=$(wc -l < "$COVERAGE_FILE") '{printf "%.2f%%\n", ($1/total)*100}')"
} >> "$STATS_FILE"

# Step 10: Clean up intermediate files
print_status "Step 10: Cleaning up intermediate files..."
rm -f "$SAM_FILE" "$BAM_FILE" "$SAM1_FILE" "$SAM2_FILE"
print_info "✓ Removed intermediate SAM and unsorted BAM files"

# Final summary
print_status "=== WORKFLOW COMPLETED SUCCESSFULLY ==="
echo ""
print_info "📁 Final Output Files:"
print_info "   • Sorted BAM: $SORTED_BAM"
print_info "   • BAM Index: ${SORTED_BAM}.bai"
print_info "   • Statistics: $STATS_FILE"
print_info "   • Coverage: $COVERAGE_FILE"
echo ""
print_info "🔬 IGV Loading Instructions:"
print_info "   1. Open IGV genome viewer"
print_info "   2. Load reference: $REFERENCE"
print_info "   3. Load BAM file: $SORTED_BAM"
print_info "   4. The .bai index will be automatically detected"
echo ""
print_info "📊 View statistics with: cat $STATS_FILE"
print_info "📈 View coverage with: head -20 $COVERAGE_FILE"
echo ""
print_status "Combined alignment of FSEM002C and FSEM001O reads to MS2_N4 reference completed!"
print_status "Ready for IGV visualization and downstream analysis."
