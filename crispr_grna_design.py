#!/usr/bin/env python3
"""
CRISPR Guide RNA Design Pipeline for Conserved Regions
Specialized for designing spatially separated guide RNAs for CRISPR-Cas9 applications

This script designs 20bp guide RNAs from conserved regions with enforced spatial separation
to ensure effective targeting across different genomic regions.
"""

import os
import sys
import argparse
from pathlib import Path
from collections import defaultdict, Counter
import logging
from typing import Dict, List, Tuple, Set, Optional
import re

# Import required packages
try:
    from Bio import SeqIO
    from Bio.Seq import Seq
    from Bio.SeqRecord import SeqRecord
    import pandas as pd
    import numpy as np
    from tqdm import tqdm
    import primer3
except ImportError as e:
    print(f"Missing required package: {e}")
    print("Please install: pip install biopython pandas numpy tqdm primer3-py")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ConservedRegion:
    """Represents a conserved region in multiple sequences."""
    
    def __init__(self, start: int, end: int, consensus: str, conservation_score: float):
        self.start = start
        self.end = end
        self.consensus = consensus
        self.conservation_score = conservation_score
        self.length = end - start + 1
    
    def __repr__(self):
        return f"ConservedRegion({self.start}-{self.end}, score={self.conservation_score:.3f}, seq={self.consensus[:20]}...)"

class CRISPRGuideDesigner:
    """Design CRISPR guide RNAs with spatial separation and PAM site validation."""
    
    def __init__(self, target_length: int = 20, min_separation: int = 100, 
                 gc_range: Tuple[float, float] = (20, 80), max_guides: int = 50):
        self.target_length = target_length
        self.min_separation = min_separation  # Minimum distance between guides
        self.gc_range = gc_range
        self.max_guides = max_guides
        
        # CRISPR-Cas9 PAM sequences (NGG for SpCas9)
        self.pam_pattern = re.compile(r'[ATCG]GG')
        
    def find_pam_sites(self, sequence: str) -> List[int]:
        """Find PAM sites (NGG) in the sequence."""
        pam_sites = []
        for match in self.pam_pattern.finditer(sequence.upper()):
            pam_sites.append(match.start())
        return pam_sites
    
    def calculate_gc_content(self, sequence: str) -> float:
        """Calculate GC content percentage."""
        gc_count = sequence.count('G') + sequence.count('C')
        return (gc_count / len(sequence)) * 100 if len(sequence) > 0 else 0
    
    def calculate_tm(self, sequence: str) -> float:
        """Calculate melting temperature using primer3."""
        try:
            return primer3.calc_tm(sequence)
        except:
            # Fallback calculation for guide RNAs
            return 64.9 + 41 * (sequence.count('G') + sequence.count('C') - 16.4) / len(sequence)
    
    def has_off_target_risk(self, sequence: str) -> bool:
        """Check for potential off-target risks (simple heuristics)."""
        # Avoid sequences with long homopolymer runs
        for base in ['A', 'T', 'G', 'C']:
            if base * 4 in sequence:  # 4+ consecutive same bases
                return True
        
        # Avoid sequences with very low or very high GC content
        gc_content = self.calculate_gc_content(sequence)
        if gc_content < 20 or gc_content > 80:
            return True
            
        return False
    
    def design_guides_with_separation(self, conserved_regions: List[ConservedRegion]) -> List[Dict]:
        """Design guide RNAs with enforced spatial separation."""
        logger.info(f"Designing guide RNAs with minimum {self.min_separation}bp separation")
        
        # Sort regions by conservation score (highest first)
        sorted_regions = sorted(conserved_regions, key=lambda x: -x.conservation_score)
        
        selected_guides = []
        used_positions = set()
        
        for region in sorted_regions:
            if len(selected_guides) >= self.max_guides:
                break
                
            sequence = region.consensus.upper()
            
            # Find PAM sites in the region
            pam_sites = self.find_pam_sites(sequence)
            
            for pam_pos in pam_sites:
                if len(selected_guides) >= self.max_guides:
                    break
                
                # Extract guide RNA sequence (20bp upstream of PAM)
                if pam_pos >= self.target_length:
                    guide_start = pam_pos - self.target_length
                    guide_seq = sequence[guide_start:pam_pos]
                    
                    if len(guide_seq) == self.target_length:
                        # Calculate genomic position
                        genomic_pos = region.start + guide_start
                        
                        # Check spatial separation from existing guides
                        too_close = False
                        for used_pos in used_positions:
                            if abs(genomic_pos - used_pos) < self.min_separation:
                                too_close = True
                                break
                        
                        if too_close:
                            continue
                        
                        # Validate guide RNA quality
                        gc_content = self.calculate_gc_content(guide_seq)
                        
                        if (self.gc_range[0] <= gc_content <= self.gc_range[1] and 
                            not self.has_off_target_risk(guide_seq)):
                            
                            # Extract PAM sequence
                            pam_seq = sequence[pam_pos:pam_pos+3] if pam_pos+3 <= len(sequence) else "N/A"
                            
                            guide = {
                                'guide_id': f"gRNA_{len(selected_guides)+1}",
                                'sequence': guide_seq,
                                'pam_sequence': pam_seq,
                                'length': len(guide_seq),
                                'gc_content': gc_content,
                                'tm': self.calculate_tm(guide_seq),
                                'conservation_score': region.conservation_score,
                                'genomic_start': genomic_pos,
                                'genomic_end': genomic_pos + self.target_length - 1,
                                'pam_position': region.start + pam_pos,
                                'source_region': f"region_{conserved_regions.index(region)+1}",
                                'strand': 'forward'
                            }
                            
                            selected_guides.append(guide)
                            used_positions.add(genomic_pos)
            
            # Also check reverse complement for PAM sites
            rev_comp = str(Seq(sequence).reverse_complement())
            rev_pam_sites = self.find_pam_sites(rev_comp)
            
            for pam_pos in rev_pam_sites:
                if len(selected_guides) >= self.max_guides:
                    break
                
                # Extract guide RNA sequence (20bp upstream of PAM on reverse strand)
                if pam_pos >= self.target_length:
                    guide_start = pam_pos - self.target_length
                    guide_seq = rev_comp[guide_start:pam_pos]
                    
                    if len(guide_seq) == self.target_length:
                        # Calculate genomic position (adjust for reverse complement)
                        genomic_pos = region.end - pam_pos
                        
                        # Check spatial separation
                        too_close = False
                        for used_pos in used_positions:
                            if abs(genomic_pos - used_pos) < self.min_separation:
                                too_close = True
                                break
                        
                        if too_close:
                            continue
                        
                        # Validate guide RNA quality
                        gc_content = self.calculate_gc_content(guide_seq)
                        
                        if (self.gc_range[0] <= gc_content <= self.gc_range[1] and 
                            not self.has_off_target_risk(guide_seq)):
                            
                            # Extract PAM sequence
                            pam_seq = rev_comp[pam_pos:pam_pos+3] if pam_pos+3 <= len(rev_comp) else "N/A"
                            
                            guide = {
                                'guide_id': f"gRNA_{len(selected_guides)+1}",
                                'sequence': guide_seq,
                                'pam_sequence': pam_seq,
                                'length': len(guide_seq),
                                'gc_content': gc_content,
                                'tm': self.calculate_tm(guide_seq),
                                'conservation_score': region.conservation_score,
                                'genomic_start': genomic_pos - self.target_length + 1,
                                'genomic_end': genomic_pos,
                                'pam_position': region.end - pam_pos + 3,
                                'source_region': f"region_{conserved_regions.index(region)+1}",
                                'strand': 'reverse'
                            }
                            
                            selected_guides.append(guide)
                            used_positions.add(genomic_pos)
        
        # Sort by conservation score and GC content
        selected_guides.sort(key=lambda x: (-x['conservation_score'], abs(x['gc_content'] - 50)))
        
        logger.info(f"Designed {len(selected_guides)} spatially separated guide RNAs")
        return selected_guides

class KmerBasedFinder:
    """Find conserved regions using k-mer analysis optimized for CRISPR design."""
    
    def __init__(self, kmer_size: int = 23, min_conservation: float = 0.8):  # 23bp = 20bp guide + 3bp PAM
        self.kmer_size = kmer_size
        self.min_conservation = min_conservation
    
    def find_conserved_regions(self, sequences: List[SeqRecord]) -> List[ConservedRegion]:
        """Find conserved regions using k-mer frequency analysis."""
        logger.info(f"Finding conserved regions using {self.kmer_size}-mers with min conservation {self.min_conservation}")
        
        if len(sequences) < 2:
            logger.warning("Need at least 2 sequences for conservation analysis")
            return []
        
        # Extract k-mers from all sequences
        kmer_positions = defaultdict(list)
        
        for i, record in enumerate(tqdm(sequences, desc="Processing sequences")):
            seq_str = str(record.seq).upper()
            
            for j in range(len(seq_str) - self.kmer_size + 1):
                kmer = seq_str[j:j + self.kmer_size]
                if 'N' not in kmer:  # Skip ambiguous sequences
                    kmer_positions[kmer].append((i, j))
        
        # Find conserved k-mers
        conserved_regions = []
        total_sequences = len(sequences)
        
        logger.info("Analyzing k-mer conservation...")
        for kmer, positions in tqdm(kmer_positions.items(), desc="Finding conserved k-mers"):
            # Calculate conservation score
            sequence_ids = set(pos[0] for pos in positions)
            conservation_score = len(sequence_ids) / total_sequences
            
            if conservation_score >= self.min_conservation:
                # Use the first occurrence position as representative
                first_pos = min(pos[1] for pos in positions if pos[0] == 0) if 0 in sequence_ids else positions[0][1]
                
                region = ConservedRegion(
                    start=first_pos,
                    end=first_pos + self.kmer_size - 1,
                    consensus=kmer,
                    conservation_score=conservation_score
                )
                conserved_regions.append(region)
        
        # Sort by conservation score and position
        conserved_regions.sort(key=lambda x: (-x.conservation_score, x.start))
        
        logger.info(f"Found {len(conserved_regions)} conserved regions")
        return conserved_regions

def load_sequences(fasta_file: str) -> List[SeqRecord]:
    """Load sequences from FASTA file."""
    logger.info(f"Loading sequences from {fasta_file}")
    
    sequences = []
    try:
        for record in SeqIO.parse(fasta_file, "fasta"):
            sequences.append(record)
        
        logger.info(f"Loaded {len(sequences)} sequences")
        return sequences
    
    except Exception as e:
        logger.error(f"Error loading sequences: {e}")
        return []

def save_results(guides: List[Dict], conserved_regions: List[ConservedRegion], output_dir: str):
    """Save guide RNA design results."""
    logger.info(f"Saving results to {output_dir}")
    
    os.makedirs(output_dir, exist_ok=True)
    
    if not guides:
        logger.warning("No guide RNAs to save")
        return
    
    # Save guide RNA results
    df = pd.DataFrame(guides)
    df.to_csv(f"{output_dir}/guide_rnas.csv", index=False)
    
    # Save guide RNA sequences as FASTA
    with open(f"{output_dir}/guide_rnas.fasta", 'w') as f:
        for guide in guides:
            f.write(f">{guide['guide_id']} | PAM:{guide['pam_sequence']} | GC:{guide['gc_content']:.1f}% | Tm:{guide['tm']:.1f}°C | Cons:{guide['conservation_score']:.3f} | {guide['strand']}\n")
            f.write(f"{guide['sequence']}\n")
    
    # Save conserved regions
    with open(f"{output_dir}/conserved_regions.txt", 'w') as f:
        f.write("Conserved Regions Found:\n")
        f.write("=" * 50 + "\n")
        for i, region in enumerate(conserved_regions):
            f.write(f"Region {i+1}:\n")
            f.write(f"  Position: {region.start}-{region.end}\n")
            f.write(f"  Length: {region.length} bp\n")
            f.write(f"  Conservation Score: {region.conservation_score:.3f}\n")
            f.write(f"  Sequence: {region.consensus}\n\n")
    
    # Save summary
    with open(f"{output_dir}/guide_rna_summary.txt", 'w') as f:
        f.write("CRISPR GUIDE RNA DESIGN SUMMARY\n")
        f.write("=" * 50 + "\n")
        f.write(f"Total conserved regions found: {len(conserved_regions)}\n")
        f.write(f"Total guide RNAs designed: {len(guides)}\n")
        
        if guides:
            df = pd.DataFrame(guides)
            f.write(f"\nGuide RNA Statistics:\n")
            f.write(f"  Average GC content: {df['gc_content'].mean():.1f}%\n")
            f.write(f"  Average Tm: {df['tm'].mean():.1f}°C\n")
            f.write(f"  Average conservation score: {df['conservation_score'].mean():.3f}\n")
            f.write(f"  Strand distribution: Forward={len(df[df['strand']=='forward'])}, Reverse={len(df[df['strand']=='reverse'])}\n")
            
            # Calculate minimum separation achieved
            positions = sorted(df['genomic_start'].tolist())
            min_sep = min([positions[i+1] - positions[i] for i in range(len(positions)-1)]) if len(positions) > 1 else 0
            f.write(f"  Minimum separation achieved: {min_sep} bp\n")
            
            f.write(f"\nTop 10 guide RNAs by conservation score:\n")
            top_guides = df.head(10)
            for _, guide in top_guides.iterrows():
                f.write(f"  {guide['guide_id']}: {guide['sequence']} (PAM:{guide['pam_sequence']}, GC:{guide['gc_content']:.1f}%, Cons:{guide['conservation_score']:.3f}, {guide['strand']})\n")

def main():
    parser = argparse.ArgumentParser(description="Design CRISPR guide RNAs from conserved regions with spatial separation")
    parser.add_argument("input_fasta", help="Input FASTA file with multiple sequences")
    parser.add_argument("-o", "--output", default="crispr_guides", help="Output directory")
    parser.add_argument("-k", "--kmer-size", type=int, default=23, help="K-mer size for conservation analysis (default: 23)")
    parser.add_argument("-c", "--conservation", type=float, default=0.8, help="Minimum conservation score (0-1)")
    parser.add_argument("-l", "--length", type=int, default=20, help="Guide RNA length (default: 20)")
    parser.add_argument("-s", "--separation", type=int, default=100, help="Minimum separation between guides (bp)")
    parser.add_argument("-n", "--max-guides", type=int, default=50, help="Maximum number of guide RNAs to design")
    parser.add_argument("--gc-min", type=float, default=20, help="Minimum GC content (%)")
    parser.add_argument("--gc-max", type=float, default=80, help="Maximum GC content (%)")
    
    args = parser.parse_args()
    
    # Load sequences
    sequences = load_sequences(args.input_fasta)
    if not sequences:
        logger.error("No sequences loaded. Exiting.")
        return 1
    
    # Find conserved regions
    finder = KmerBasedFinder(kmer_size=args.kmer_size, min_conservation=args.conservation)
    conserved_regions = finder.find_conserved_regions(sequences)
    
    if not conserved_regions:
        logger.error("No conserved regions found. Try lowering conservation threshold.")
        return 1
    
    # Design guide RNAs
    designer = CRISPRGuideDesigner(
        target_length=args.length,
        min_separation=args.separation,
        gc_range=(args.gc_min, args.gc_max),
        max_guides=args.max_guides
    )
    guides = designer.design_guides_with_separation(conserved_regions)
    
    if not guides:
        logger.error("No suitable guide RNAs found. Try relaxing parameters.")
        return 1
    
    # Save results
    save_results(guides, conserved_regions, args.output)
    
    # Print summary
    print(f"\n=== CRISPR GUIDE RNA DESIGN COMPLETED ===")
    print(f"Total guide RNAs designed: {len(guides)}")
    print(f"Results saved in: {args.output}")
    
    if len(guides) > 0:
        df = pd.DataFrame(guides)
        print(f"\nTop 10 guide RNAs:")
        top_guides = df.head(10)
        for _, guide in top_guides.iterrows():
            print(f"  {guide['guide_id']}: {guide['sequence']} (PAM:{guide['pam_sequence']}, GC:{guide['gc_content']:.1f}%, Cons:{guide['conservation_score']:.3f})")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
