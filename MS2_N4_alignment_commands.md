# MS2_N4 Combined Read Alignment Commands

## Topic: High-Coverage Viral Genome Alignment for IGV Visualization

This document provides all the commands needed to align FSEM002C_S40_L007 and FSEM001O_S20_L008 reads to the MS2_N4 reference genome, creating a combined BAM file for maximum coverage analysis in IGV.

## Quick Start Commands

### 1. Navigate to the working directory
```bash
cd /clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/bwa-env
```

### 2. Run the complete alignment workflow
```bash
./combined_MS2_N4_alignment.sh
```

## Manual Step-by-Step Commands

If you prefer to run commands individually:

### 1. Activate the environment and navigate
```bash
cd /clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/bwa-env
```

### 2. Index the reference genome (if not already done)
```bash
pixi run bwa-mem2 index /clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/MS2_N4.fna
```

### 3. Align both read files simultaneously
```bash
pixi run bwa-mem2 mem -t 8 -M /clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/MS2_N4.fna \
  /clusterfs/jgi/scratch/science/mgs/nelli/databases/ww/oakland/00data/readsf_10M/FSEM002C_S40_L007.anqdpht.fastq.gz \
  /clusterfs/jgi/scratch/science/mgs/nelli/databases/ww/oakland/00data/readsf_10M/FSEM001O_S20_L008.anqdpht.fastq.gz \
  > MS2_N4_combined_FSEM002C_FSEM001O.sam
```

### 4. Convert SAM to BAM
```bash
pixi run samtools view -bS MS2_N4_combined_FSEM002C_FSEM001O.sam > MS2_N4_combined_FSEM002C_FSEM001O.bam
```

### 5. Sort the BAM file
```bash
pixi run samtools sort MS2_N4_combined_FSEM002C_FSEM001O.bam -o MS2_N4_combined_FSEM002C_FSEM001O.sorted.bam
```

### 6. Index the sorted BAM file
```bash
pixi run samtools index MS2_N4_combined_FSEM002C_FSEM001O.sorted.bam
```

### 7. Generate alignment statistics
```bash
pixi run samtools flagstat MS2_N4_combined_FSEM002C_FSEM001O.sorted.bam > alignment_stats.txt
```

### 8. Generate coverage analysis
```bash
pixi run samtools depth MS2_N4_combined_FSEM002C_FSEM001O.sorted.bam > coverage_analysis.txt
```

## File Paths Reference

- **Reference Genome**: `/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/MS2_N4.fna`
- **Read File 1**: `/clusterfs/jgi/scratch/science/mgs/nelli/databases/ww/oakland/00data/readsf_10M/FSEM002C_S40_L007.anqdpht.fastq.gz`
- **Read File 2**: `/clusterfs/jgi/scratch/science/mgs/nelli/databases/ww/oakland/00data/readsf_10M/FSEM001O_S20_L008.anqdpht.fastq.gz`
- **Working Directory**: `/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/bwa-env`

## Output Files

After successful completion, you will have:

- `MS2_N4_combined_FSEM002C_FSEM001O.sorted.bam` - Main alignment file for IGV
- `MS2_N4_combined_FSEM002C_FSEM001O.sorted.bam.bai` - Index file for IGV
- `MS2_N4_combined_FSEM002C_FSEM001O_stats.txt` - Alignment statistics
- `MS2_N4_combined_FSEM002C_FSEM001O_coverage.txt` - Coverage analysis

## IGV Loading Instructions

1. Open IGV genome viewer
2. Load reference genome: File → Load Genome → Select MS2_N4.fna
3. Load BAM file: File → Load from File → Select the .sorted.bam file
4. The .bai index will be automatically detected
5. Navigate to view coverage peaks and mapped reads

## Quality Check Commands

### View alignment statistics
```bash
cat MS2_N4_combined_FSEM002C_FSEM001O_stats.txt
```

### View coverage summary
```bash
head -20 MS2_N4_combined_FSEM002C_FSEM001O_coverage.txt
```

### Calculate mean coverage
```bash
awk '{sum+=$3; count++} END {printf "Mean coverage: %.2f\n", sum/count}' MS2_N4_combined_FSEM002C_FSEM001O_coverage.txt
```

## Environment Information

- **BWA Version**: bwa-mem2 (high-performance version)
- **Samtools Version**: >=1.22.1
- **Environment Manager**: pixi
- **Platform**: linux-64

## Notes

- The combined alignment approach maximizes read coverage by including reads from both FSEM002C and FSEM001O samples
- BWA-MEM2 is used for high-performance alignment
- All intermediate files are cleaned up automatically by the script
- The final BAM file is coordinate-sorted and indexed for optimal IGV performance
