#!/usr/bin/env python3
"""
CRISPR Guide RNA Design Pipeline - Final Version
==============================================

Advanced CRISPR guide RNA design from conserved regions in viral sequences.
Designed for senior bioinformaticians working with viral genome analysis.

Features:
- Conserved region detection using k-mer analysis
- Spatial separation between guide RNAs (prevents clustering)
- PAM site recognition (NGG, NAG for SpCas9)
- Quality filtering (GC content, melting temperature)
- Comprehensive output formats

Author: Senior Bioinformatics Pipeline
Date: August 2025
"""

import sys
import os
import argparse
import re
from collections import defaultdict, Counter
from typing import List, Dict, Tuple, Set
import csv

class CRISPRGuideDesigner:
    """Advanced CRISPR guide RNA designer with conserved region analysis."""
    
    def __init__(self, guide_length=20, min_separation=150, min_conservation=0.5):
        self.guide_length = guide_length
        self.min_separation = min_separation
        self.min_conservation = min_conservation
        self.pam_patterns = ['GG', 'AG']  # NGG, NAG for SpCas9
        self.sequences = []
        self.sequence_names = []
        
    def load_fasta(self, fasta_file: str) -> None:
        """Load sequences from FASTA file."""
        print(f"Loading sequences from {fasta_file}...")
        
        with open(fasta_file, 'r') as f:
            current_seq = ""
            current_name = ""
            
            for line in f:
                line = line.strip()
                if line.startswith('>'):
                    if current_seq and current_name:
                        self.sequences.append(current_seq.upper())
                        self.sequence_names.append(current_name)
                    current_name = line[1:].split()[0]  # Take first word after >
                    current_seq = ""
                else:
                    current_seq += line
            
            # Add last sequence
            if current_seq and current_name:
                self.sequences.append(current_seq.upper())
                self.sequence_names.append(current_name)
        
        print(f"Loaded {len(self.sequences)} sequences")
        if len(self.sequences) == 0:
            raise ValueError("No sequences found in FASTA file")
    
    def find_conserved_kmers(self, k: int = 23) -> Dict[str, float]:
        """Find conserved k-mers across sequences."""
        print(f"Analyzing {k}-mer conservation across {len(self.sequences)} sequences...")
        
        kmer_counts = defaultdict(int)
        total_sequences = len(self.sequences)
        
        # Count k-mers in each sequence
        for seq in self.sequences:
            seen_kmers = set()
            for i in range(len(seq) - k + 1):
                kmer = seq[i:i+k]
                if kmer not in seen_kmers:
                    kmer_counts[kmer] += 1
                    seen_kmers.add(kmer)
        
        # Calculate conservation scores
        conservation_scores = {}
        for kmer, count in kmer_counts.items():
            conservation_scores[kmer] = count / total_sequences
        
        # Filter by minimum conservation
        conserved_kmers = {k: v for k, v in conservation_scores.items() 
                          if v >= self.min_conservation}
        
        print(f"Found {len(conserved_kmers)} conserved {k}-mers (≥{self.min_conservation} conservation)")
        return conserved_kmers
    
    def find_pam_sites(self, sequence: str) -> List[Tuple[int, str, str]]:
        """Find PAM sites in sequence. Returns (position, pam_sequence, strand)."""
        pam_sites = []
        
        # Forward strand PAM sites (NGG, NAG)
        for pattern in self.pam_patterns:
            for match in re.finditer(f'[ATCG]{pattern}', sequence):
                pos = match.start()
                pam_seq = match.group()
                pam_sites.append((pos, pam_seq, 'forward'))
        
        # Reverse strand PAM sites (CCN, CTN)
        reverse_patterns = ['CC', 'CT']  # Complement of GG, AG
        for pattern in reverse_patterns:
            for match in re.finditer(f'{pattern}[ATCG]', sequence):
                pos = match.start()
                pam_seq = match.group()
                pam_sites.append((pos, pam_seq, 'reverse'))
        
        return pam_sites
    
    def extract_guide_sequence(self, sequence: str, pam_pos: int, strand: str) -> str:
        """Extract guide RNA sequence based on PAM position and strand."""
        if strand == 'forward':
            # Guide is upstream of PAM
            start = pam_pos - self.guide_length
            if start < 0:
                return None
            return sequence[start:pam_pos]
        else:
            # Guide is downstream of PAM (reverse complement)
            start = pam_pos + 3  # PAM is 3bp
            if start + self.guide_length > len(sequence):
                return None
            guide = sequence[start:start + self.guide_length]
            return self.reverse_complement(guide)
    
    def reverse_complement(self, seq: str) -> str:
        """Return reverse complement of DNA sequence."""
        complement = {'A': 'T', 'T': 'A', 'G': 'C', 'C': 'G'}
        return ''.join(complement.get(base, base) for base in reversed(seq))
    
    def calculate_gc_content(self, seq: str) -> float:
        """Calculate GC content percentage."""
        gc_count = seq.count('G') + seq.count('C')
        return (gc_count / len(seq)) * 100 if len(seq) > 0 else 0
    
    def calculate_melting_temp(self, seq: str) -> float:
        """Calculate approximate melting temperature."""
        # Simple formula: Tm = 2*(A+T) + 4*(G+C)
        at_count = seq.count('A') + seq.count('T')
        gc_count = seq.count('G') + seq.count('C')
        return 2 * at_count + 4 * gc_count
    
    def is_valid_guide(self, guide_seq: str, gc_min: float = 20, gc_max: float = 80) -> bool:
        """Check if guide RNA meets quality criteria."""
        if len(guide_seq) != self.guide_length:
            return False
        
        # Check for valid nucleotides only
        if not all(base in 'ATCG' for base in guide_seq):
            return False
        
        # Check GC content
        gc_content = self.calculate_gc_content(guide_seq)
        if gc_content < gc_min or gc_content > gc_max:
            return False
        
        # Check for poly-T (problematic for transcription)
        if 'TTTT' in guide_seq:
            return False
        
        return True
    
    def enforce_spatial_separation(self, candidates: List[Dict], min_sep: int) -> List[Dict]:
        """Enforce minimum separation between guide RNAs."""
        if not candidates:
            return []
        
        # Sort by conservation score (descending)
        sorted_candidates = sorted(candidates, key=lambda x: x['conservation_score'], reverse=True)
        
        selected = []
        for candidate in sorted_candidates:
            # Check distance from all selected guides
            too_close = False
            for selected_guide in selected:
                distance = abs(candidate['position'] - selected_guide['position'])
                if distance < min_sep:
                    too_close = True
                    break
            
            if not too_close:
                selected.append(candidate)
        
        return selected

    def design_guides(self, max_guides: int = 10, gc_min: float = 20, gc_max: float = 80) -> List[Dict]:
        """Design CRISPR guide RNAs from conserved regions."""
        print("Designing CRISPR guide RNAs...")

        # Find conserved k-mers
        conserved_kmers = self.find_conserved_kmers(self.guide_length)

        if not conserved_kmers:
            print("No conserved regions found. Try lowering conservation threshold.")
            return []

        # Find guide RNA candidates
        candidates = []
        guide_id = 1

        # Use first sequence as reference for PAM site detection
        reference_seq = self.sequences[0]
        pam_sites = self.find_pam_sites(reference_seq)

        print(f"Found {len(pam_sites)} potential PAM sites")

        for pam_pos, pam_seq, strand in pam_sites:
            guide_seq = self.extract_guide_sequence(reference_seq, pam_pos, strand)

            if guide_seq and self.is_valid_guide(guide_seq, gc_min, gc_max):
                # Check if this guide sequence is conserved
                conservation_score = conserved_kmers.get(guide_seq, 0.0)

                if conservation_score >= self.min_conservation:
                    candidates.append({
                        'guide_id': f'gRNA_{guide_id}',
                        'sequence': guide_seq,
                        'pam_sequence': pam_seq,
                        'position': pam_pos,
                        'strand': strand,
                        'conservation_score': conservation_score,
                        'gc_content': self.calculate_gc_content(guide_seq),
                        'melting_temp': self.calculate_melting_temp(guide_seq),
                        'length': len(guide_seq)
                    })
                    guide_id += 1

        print(f"Found {len(candidates)} valid guide RNA candidates")

        # Enforce spatial separation
        separated_guides = self.enforce_spatial_separation(candidates, self.min_separation)

        # Limit to max_guides
        final_guides = separated_guides[:max_guides]

        print(f"Selected {len(final_guides)} spatially separated guide RNAs")
        return final_guides

    def save_results(self, guides: List[Dict], output_dir: str) -> None:
        """Save guide RNA results in multiple formats."""
        os.makedirs(output_dir, exist_ok=True)

        if not guides:
            print("No guide RNAs to save.")
            return

        # Save FASTA format
        fasta_file = os.path.join(output_dir, 'guide_rnas.fasta')
        with open(fasta_file, 'w') as f:
            for guide in guides:
                header = f">{guide['guide_id']} PAM:{guide['pam_sequence']} GC:{guide['gc_content']:.1f}% Tm:{guide['melting_temp']:.1f}C Cons:{guide['conservation_score']:.3f} {guide['strand']}"
                f.write(f"{header}\n{guide['sequence']}\n")

        # Save CSV format
        csv_file = os.path.join(output_dir, 'guide_rnas.csv')
        with open(csv_file, 'w', newline='') as f:
            fieldnames = ['guide_id', 'sequence', 'pam_sequence', 'length', 'gc_content',
                         'melting_temp', 'conservation_score', 'position', 'strand']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(guides)

        # Save summary
        summary_file = os.path.join(output_dir, 'guide_rna_summary.txt')
        with open(summary_file, 'w') as f:
            f.write("CRISPR Guide RNA Design Summary\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"Input sequences: {len(self.sequences)}\n")
            f.write(f"Guide RNAs designed: {len(guides)}\n")
            f.write(f"Minimum separation: {self.min_separation} bp\n")
            f.write(f"Conservation threshold: {self.min_conservation}\n\n")

            f.write("Top Guide RNAs (by conservation score):\n")
            f.write("-" * 50 + "\n")
            for i, guide in enumerate(guides[:10], 1):
                f.write(f"{i:2d}. {guide['guide_id']}: {guide['sequence']}\n")
                f.write(f"    Conservation: {guide['conservation_score']:.3f}, GC: {guide['gc_content']:.1f}%, Tm: {guide['melting_temp']:.1f}°C\n")
                f.write(f"    Position: {guide['position']}, PAM: {guide['pam_sequence']}, Strand: {guide['strand']}\n\n")

        print(f"Results saved to {output_dir}/")
        print(f"  - guide_rnas.fasta: FASTA format sequences")
        print(f"  - guide_rnas.csv: Detailed information")
        print(f"  - guide_rna_summary.txt: Summary and top guides")


def main():
    """Main function for command-line execution."""
    parser = argparse.ArgumentParser(
        description="CRISPR Guide RNA Design Pipeline - Final Version",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s -i pathdb.fna
  %(prog)s -i viral_sequences.fasta -n 50 -s 200 -c 0.8
  %(prog)s -i sequences.fasta -o my_guides --gc-min 30 --gc-max 70
        """
    )

    parser.add_argument('-i', '--input', required=True,
                       help='Input FASTA file with viral sequences')
    parser.add_argument('-o', '--output', default='crispr_guides_final',
                       help='Output directory (default: crispr_guides_final)')
    parser.add_argument('-n', '--max-guides', type=int, default=10,
                       help='Maximum number of guide RNAs (default: 10)')
    parser.add_argument('-s', '--separation', type=int, default=150,
                       help='Minimum separation between guides in bp (default: 150)')
    parser.add_argument('-c', '--conservation', type=float, default=0.5,
                       help='Minimum conservation score 0-1 (default: 0.5)')
    parser.add_argument('-l', '--length', type=int, default=20,
                       help='Guide RNA length (default: 20)')
    parser.add_argument('--gc-min', type=float, default=20,
                       help='Minimum GC content percentage (default: 20)')
    parser.add_argument('--gc-max', type=float, default=80,
                       help='Maximum GC content percentage (default: 80)')

    args = parser.parse_args()

    # Validate input file
    if not os.path.exists(args.input):
        print(f"Error: Input file '{args.input}' not found.")
        sys.exit(1)

    # Initialize designer
    designer = CRISPRGuideDesigner(
        guide_length=args.length,
        min_separation=args.separation,
        min_conservation=args.conservation
    )

    try:
        # Load sequences
        designer.load_fasta(args.input)

        # Design guide RNAs
        guides = designer.design_guides(
            max_guides=args.max_guides,
            gc_min=args.gc_min,
            gc_max=args.gc_max
        )

        # Save results
        designer.save_results(guides, args.output)

        print(f"\nCRISPR guide RNA design completed successfully!")
        print(f"Check {args.output}/ for results.")

    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
