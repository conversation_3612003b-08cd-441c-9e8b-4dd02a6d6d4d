# CRISPR Guide RNA Design Pipeline - Final Commands Summary

## 🎯 **CRISPR Guide RNA Design Pipeline-Final**

### **Complete Pipeline for Senior Bioinformaticians**

This document provides all the commands and file locations for the CRISPR Guide RNA Design Pipeline designed specifically for analyzing pathdb.fna and generating high-quality guide RNAs with spatial separation.

---

## 📁 **File Locations and Setup**

### **Current Files (Ready to Copy)**
```bash
# Files created in current directory:
/clusterfs/jgi/groups/science/homes/rthapamagar/.ssh/
├── crispr_guide_design_final.py         # Main Python script
├── run_crispr_pipeline_final.sh         # Bash wrapper script  
├── setup_crispr_pipeline.sh             # Automated setup script
├── CRISPR_Pipeline_Documentation.md     # Complete documentation
└── CRISPR_COMMANDS_SUMMARY.md           # This summary file
```

### **Target Directory Setup**
```bash
# Target directory for the pipeline:
/clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign/
├── pathdb.fna                           # Your input viral sequences
├── crispr_guide_design_final.py         # Main Python script
├── run_crispr_pipeline_final.sh         # Bash wrapper script
├── CRISPR_Pipeline_Documentation.md     # Documentation
└── crispr_guides_final/                 # Output directory (created after run)
    ├── guide_rnas.fasta                 # Guide sequences for ordering
    ├── guide_rnas.csv                   # Detailed guide information
    └── guide_rna_summary.txt            # Summary report
```

---

## 🚀 **Installation Commands**

### **Method 1: Automated Setup (Recommended)**
```bash
# Run the automated setup script
./setup_crispr_pipeline.sh

# Or specify custom target directory
./setup_crispr_pipeline.sh -t /path/to/your/directory
```

### **Method 2: Manual Setup**
```bash
# 1. Navigate to target directory
cd /clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign

# 2. Copy files from current location
cp /clusterfs/jgi/groups/science/homes/rthapamagar/.ssh/crispr_guide_design_final.py .
cp /clusterfs/jgi/groups/science/homes/rthapamagar/.ssh/run_crispr_pipeline_final.sh .
cp /clusterfs/jgi/groups/science/homes/rthapamagar/.ssh/CRISPR_Pipeline_Documentation.md .

# 3. Make scripts executable
chmod +x crispr_guide_design_final.py
chmod +x run_crispr_pipeline_final.sh

# 4. Verify pathdb.fna exists
ls -la pathdb.fna
head -5 pathdb.fna
```

---

## 🧬 **Pipeline Execution Commands**

### **Basic Usage (10 guide RNAs)**
```bash
cd /clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign
./run_crispr_pipeline_final.sh -i pathdb.fna
```

### **High-Quality Guides (20 guides, high conservation)**
```bash
./run_crispr_pipeline_final.sh -i pathdb.fna -n 20 -c 0.8 -s 200 -o high_quality_guides
```

### **Comprehensive Coverage (50 guides)**
```bash
./run_crispr_pipeline_final.sh -i pathdb.fna -n 50 -c 0.5 -s 150 -o comprehensive_guides
```

### **Exploratory Analysis (100 guides, relaxed parameters)**
```bash
./run_crispr_pipeline_final.sh -i pathdb.fna -n 100 -c 0.3 -s 100 --gc-min 30 --gc-max 70 -o exploratory_guides
```

### **Verbose Output for Debugging**
```bash
./run_crispr_pipeline_final.sh -i pathdb.fna -v
```

---

## 📊 **Parameter Reference**

| Parameter | Description | Default | Recommended Range |
|-----------|-------------|---------|-------------------|
| `-i, --input` | Input FASTA file | **Required** | pathdb.fna |
| `-o, --output` | Output directory | crispr_guides_final | Any valid path |
| `-n, --max-guides` | Maximum guide RNAs | 10 | 10-100 |
| `-s, --separation` | Min separation (bp) | 150 | 100-300 |
| `-c, --conservation` | Conservation score | 0.5 | 0.3-0.9 |
| `-l, --length` | Guide RNA length | 20 | 18-22 |
| `--gc-min` | Min GC content (%) | 20 | 15-30 |
| `--gc-max` | Max GC content (%) | 80 | 70-85 |

---

## 📋 **Analysis Commands**

### **View Results**
```bash
# Navigate to output directory
cd crispr_guides_final

# View guide RNA sequences
cat guide_rnas.fasta

# View detailed information
cat guide_rnas.csv

# View summary report
cat guide_rna_summary.txt

# Count generated guides
grep -c "^>" guide_rnas.fasta
```

### **Quick Analysis**
```bash
# Show top 5 guides
head -10 crispr_guides_final/guide_rnas.fasta

# Show conservation scores
awk -F',' 'NR>1 {print $1, $7}' crispr_guides_final/guide_rnas.csv

# Show GC content distribution
awk -F',' 'NR>1 {print $5}' crispr_guides_final/guide_rnas.csv | sort -n
```

---

## 🔧 **Troubleshooting Commands**

### **Check Installation**
```bash
# Verify Python script
python3 crispr_guide_design_final.py --help

# Verify bash script
./run_crispr_pipeline_final.sh --help

# Check file permissions
ls -la crispr_guide_design_final.py run_crispr_pipeline_final.sh
```

### **Validate Input File**
```bash
# Check pathdb.fna format
head -5 pathdb.fna
grep -c "^>" pathdb.fna
file pathdb.fna
```

### **Debug Pipeline Issues**
```bash
# Run with verbose output
./run_crispr_pipeline_final.sh -i pathdb.fna -v

# Test with minimal parameters
./run_crispr_pipeline_final.sh -i pathdb.fna -n 5 -c 0.1 -s 50

# Check Python directly
python3 crispr_guide_design_final.py -i pathdb.fna -n 5 -c 0.1
```

---

## 📈 **Use Case Scenarios**

### **Scenario 1: High-Specificity Targeting**
```bash
# For closely related viral sequences requiring high specificity
./run_crispr_pipeline_final.sh -i pathdb.fna -n 10 -c 0.9 -s 200 -o high_specificity
```

### **Scenario 2: Broad Coverage Analysis**
```bash
# For diverse viral sequences requiring broad coverage
./run_crispr_pipeline_final.sh -i pathdb.fna -n 50 -c 0.5 -s 150 -o broad_coverage
```

### **Scenario 3: Exploratory Research**
```bash
# For initial exploration with many candidate guides
./run_crispr_pipeline_final.sh -i pathdb.fna -n 100 -c 0.3 -s 100 -o exploratory
```

### **Scenario 4: Custom GC Content**
```bash
# For sequences with specific GC requirements
./run_crispr_pipeline_final.sh -i pathdb.fna --gc-min 35 --gc-max 65 -o custom_gc
```

---

## 🧪 **Validation and Testing**

### **Test Pipeline Installation**
```bash
# Quick test with minimal output
./run_crispr_pipeline_final.sh -i pathdb.fna -n 3 -c 0.1 -o test_run

# Verify output files
ls -la test_run/
cat test_run/guide_rna_summary.txt
```

### **Validate Guide RNA Quality**
```bash
# Check guide RNA sequences
grep -v "^>" crispr_guides_final/guide_rnas.fasta | while read seq; do
    echo "Length: ${#seq}, Sequence: $seq"
done

# Verify conservation scores
awk -F',' 'NR>1 {if($7 >= 0.5) print $1, $7}' crispr_guides_final/guide_rnas.csv
```

---

## 📚 **Documentation and Help**

### **View Documentation**
```bash
# Read complete documentation
cat CRISPR_Pipeline_Documentation.md

# View command help
./run_crispr_pipeline_final.sh --help

# Python script help
python3 crispr_guide_design_final.py --help
```

### **Example Output Format**
```bash
# FASTA format example:
>gRNA_1 PAM:TGG GC:35.0% Tm:54.0C Cons:1.000 forward
GATTATATGGGTTTTTTCGC

# CSV format example:
guide_id,sequence,pam_sequence,length,gc_content,tm,conservation_score,position,strand
gRNA_1,GATTATATGGGTTTTTTCGC,TGG,20,35.0,54.0,1.000,1234,forward
```

---

## 🎯 **Final Workflow Summary**

### **Complete Workflow for pathdb.fna Analysis**
```bash
# 1. Setup (one-time)
cd /clusterfs/jgi/groups/science/homes/rthapamagar/.ssh
./setup_crispr_pipeline.sh

# 2. Navigate to working directory
cd /clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign

# 3. Run pipeline
./run_crispr_pipeline_final.sh -i pathdb.fna -n 20 -c 0.7 -s 150

# 4. Analyze results
cat crispr_guides_final/guide_rna_summary.txt
head -20 crispr_guides_final/guide_rnas.fasta

# 5. Ready for experimental validation!
```

---

**🚀 Your CRISPR Guide RNA Design Pipeline is ready for senior bioinformatics analysis!**
